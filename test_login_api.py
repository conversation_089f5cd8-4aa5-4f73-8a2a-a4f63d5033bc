#!/usr/bin/env python3
"""
Test script to verify login API functionality
"""
import requests
import json

def test_login_api():
    """Test the login API endpoint"""
    print("Testing Login API...")
    
    # Test data
    login_data = {
        "username": "so<PERSON><PERSON>",
        "password": "12345678"
    }
    
    try:
        # Make login request
        response = requests.post(
            "http://localhost:5002/api/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✓ Login successful!")
            print(f"  Token received: {data.get('token', 'N/A')[:50]}...")
            print(f"  User ID: {data.get('user', {}).get('id')}")
            print(f"  Username: {data.get('user', {}).get('username')}")
            print(f"  Role: {data.get('user', {}).get('role')}")
            print(f"  Tenant ID: {data.get('user', {}).get('tenant_id')}")
            
            # Test user management API with the token
            token = data.get('token')
            if token:
                print("\nTesting User Management API...")
                headers = {"Authorization": f"Bearer {token}"}
                
                users_response = requests.get(
                    "http://localhost:5002/api/admin/users",
                    headers=headers
                )
                
                print(f"Users API Status: {users_response.status_code}")
                
                if users_response.status_code == 200:
                    users_data = users_response.json()
                    print(f"✓ User management API accessible! Found {len(users_data)} users")
                    
                    # Check if soorya user is in the list
                    soorya_in_list = any(u.get('username') == 'soorya' for u in users_data)
                    if soorya_in_list:
                        print("✓ Soorya user appears in user management list")
                    else:
                        print("✗ Soorya user NOT in user management list")
                else:
                    print(f"✗ User management API failed: {users_response.text}")
            
        else:
            print(f"✗ Login failed: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to server. Make sure the backend is running on localhost:5002")
    except Exception as e:
        print(f"✗ Error: {str(e)}")

if __name__ == "__main__":
    test_login_api()
