#!/usr/bin/env python3
"""
API Endpoints Analysis Script for AVINI Labs
Maps all backend routes and frontend API calls to understand data access patterns
"""

import os
import re
from collections import defaultdict

def analyze_backend_routes():
    """Analyze all backend route files"""
    routes_dir = 'backend/routes'
    routes_info = {}
    
    if not os.path.exists(routes_dir):
        return routes_info
    
    for file in os.listdir(routes_dir):
        if file.endswith('.py') and not file.startswith('__'):
            file_path = os.path.join(routes_dir, file)
            routes_info[file] = analyze_route_file(file_path)
    
    return routes_info

def analyze_route_file(file_path):
    """Analyze a single route file"""
    routes = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find route decorators and their functions
        route_pattern = r'@\w+\.route\([\'"]([^\'"]+)[\'"](?:,\s*methods=\[([^\]]+)\])?\)'
        function_pattern = r'def\s+(\w+)\([^)]*\):'
        
        lines = content.split('\n')
        for i, line in enumerate(lines):
            route_match = re.search(route_pattern, line)
            if route_match:
                endpoint = route_match.group(1)
                methods = route_match.group(2) if route_match.group(2) else "'GET'"
                methods = [m.strip().strip('\'"') for m in methods.split(',')]
                
                # Find the function name in the next few lines
                function_name = None
                for j in range(i+1, min(i+5, len(lines))):
                    func_match = re.search(function_pattern, lines[j])
                    if func_match:
                        function_name = func_match.group(1)
                        break
                
                routes.append({
                    'endpoint': endpoint,
                    'methods': methods,
                    'function': function_name,
                    'line': i + 1
                })
    
    except Exception as e:
        print(f"Error analyzing {file_path}: {e}")
    
    return routes

def analyze_frontend_api_calls():
    """Analyze frontend API service files"""
    services_dir = 'src/services'
    api_calls = {}
    
    if not os.path.exists(services_dir):
        return api_calls
    
    for file in os.listdir(services_dir):
        if file.endswith('.js'):
            file_path = os.path.join(services_dir, file)
            api_calls[file] = analyze_api_service_file(file_path)
    
    return api_calls

def analyze_api_service_file(file_path):
    """Analyze a single API service file"""
    api_calls = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find API calls (get, post, put, delete)
        api_pattern = r'api\.(get|post|put|delete)\([\'"]([^\'"]+)[\'"]'
        
        for match in re.finditer(api_pattern, content):
            method = match.group(1).upper()
            endpoint = match.group(2)
            api_calls.append({
                'method': method,
                'endpoint': endpoint
            })
    
    except Exception as e:
        print(f"Error analyzing {file_path}: {e}")
    
    return api_calls

def analyze_data_flow():
    """Analyze data flow patterns"""
    # Analyze utils.py for data access patterns
    utils_file = 'backend/utils.py'
    data_operations = []
    
    if os.path.exists(utils_file):
        try:
            with open(utils_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find read_data and write_data calls
            read_pattern = r'read_data\([\'"]([^\'"]+)[\'"]'
            write_pattern = r'write_data\([\'"]([^\'"]+)[\'"]'
            
            for match in re.finditer(read_pattern, content):
                data_operations.append({
                    'operation': 'READ',
                    'file': match.group(1)
                })
            
            for match in re.finditer(write_pattern, content):
                data_operations.append({
                    'operation': 'WRITE',
                    'file': match.group(1)
                })
        
        except Exception as e:
            print(f"Error analyzing utils.py: {e}")
    
    return data_operations

def main():
    """Main analysis function"""
    print("API Endpoints and Data Flow Analysis")
    print("=" * 80)
    
    # Analyze backend routes
    print("\n1. BACKEND ROUTES ANALYSIS")
    print("-" * 40)
    
    backend_routes = analyze_backend_routes()
    total_endpoints = 0
    
    for file, routes in backend_routes.items():
        if routes:
            print(f"\n{file}:")
            for route in routes:
                print(f"  {route['methods']} {route['endpoint']} -> {route['function']}")
                total_endpoints += len(route['methods'])
    
    print(f"\nTotal backend endpoints: {total_endpoints}")
    
    # Analyze frontend API calls
    print("\n\n2. FRONTEND API CALLS ANALYSIS")
    print("-" * 40)
    
    frontend_apis = analyze_frontend_api_calls()
    total_api_calls = 0
    
    for file, calls in frontend_apis.items():
        if calls:
            print(f"\n{file}:")
            unique_calls = {}
            for call in calls:
                key = f"{call['method']} {call['endpoint']}"
                unique_calls[key] = call
            
            for key, call in unique_calls.items():
                print(f"  {call['method']} {call['endpoint']}")
                total_api_calls += 1
    
    print(f"\nTotal frontend API calls: {total_api_calls}")
    
    # Analyze data flow
    print("\n\n3. DATA FLOW ANALYSIS")
    print("-" * 40)
    
    data_operations = analyze_data_flow()
    read_ops = [op for op in data_operations if op['operation'] == 'READ']
    write_ops = [op for op in data_operations if op['operation'] == 'WRITE']
    
    print(f"Data read operations: {len(read_ops)}")
    print(f"Data write operations: {len(write_ops)}")
    
    # Group by file
    file_operations = defaultdict(list)
    for op in data_operations:
        file_operations[op['file']].append(op['operation'])
    
    print(f"\nFiles accessed: {len(file_operations)}")
    for file, ops in sorted(file_operations.items()):
        read_count = ops.count('READ')
        write_count = ops.count('WRITE')
        print(f"  {file}: {read_count} reads, {write_count} writes")
    
    # Summary
    print("\n\n4. SUMMARY")
    print("-" * 40)
    print(f"Backend route files: {len(backend_routes)}")
    print(f"Frontend service files: {len(frontend_apis)}")
    print(f"Total API endpoints: {total_endpoints}")
    print(f"Total API calls: {total_api_calls}")
    print(f"Data files accessed: {len(file_operations)}")

if __name__ == "__main__":
    main()
