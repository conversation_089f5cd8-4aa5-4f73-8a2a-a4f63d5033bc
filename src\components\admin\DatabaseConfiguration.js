import React, { useState, useEffect } from 'react';
import { 
  Card, 
  <PERSON>, 
  Button, 
  Row, 
  Col, 
  Alert, 
  Tabs, 
  Tab, 
  Table, 
  Modal, 
  Badge,
  Accordion,
  Spinner,
  ButtonGroup
} from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faDatabase,
  faTable,
  faPlus,
  faEdit,
  faTrash,
  faEye,
  faColumns,
  faSave,
  faSpinner,
  faCheckCircle,
  faExclamationTriangle,
  faInfoCircle,
  faCode
} from '@fortawesome/free-solid-svg-icons';
import { adminAPI } from '../../services/api';
import {
  TextInput,
  SelectInput,
  SuccessModal,
  ErrorModal
} from '../common';
import '../../styles/DatabaseConfiguration.css';

const DatabaseConfiguration = () => {
  // State management
  const [loading, setLoading] = useState(false);
  const [tables, setTables] = useState([]);
  const [selectedTable, setSelectedTable] = useState(null);
  const [dataTypes, setDataTypes] = useState([]);
  const [activeTab, setActiveTab] = useState('schema-viewer');
  
  // Modal states
  const [showAddFieldModal, setShowAddFieldModal] = useState(false);
  const [showCreateTableModal, setShowCreateTableModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  
  // Form states
  const [newField, setNewField] = useState({
    field_name: '',
    field_type: 'TEXT',
    is_nullable: true,
    is_unique: false,
    default_value: ''
  });
  
  const [newTable, setNewTable] = useState({
    table_name: '',
    fields: [
      {
        name: 'id',
        type: 'INTEGER',
        is_primary_key: true,
        is_nullable: false,
        is_unique: false,
        default_value: ''
      }
    ]
  });

  // Load initial data
  useEffect(() => {
    loadTablesWithSchema();
    loadDataTypes();
  }, []);

  const loadTablesWithSchema = async () => {
    setLoading(true);
    try {
      const response = await adminAPI.get('/admin/database/schema/tables');
      setTables(response.data.tables || []);
      if (response.data.tables && response.data.tables.length > 0) {
        setSelectedTable(response.data.tables[0]);
      }
    } catch (error) {
      setErrorMessage('Failed to load database schema: ' + error.message);
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const loadDataTypes = async () => {
    try {
      const response = await adminAPI.get('/admin/database/schema/data-types');
      setDataTypes(response.data.data_types || []);
    } catch (error) {
      console.error('Failed to load data types:', error);
    }
  };

  const handleAddField = async () => {
    if (!selectedTable || !newField.field_name || !newField.field_type) {
      setErrorMessage('Please fill in all required fields');
      setShowErrorModal(true);
      return;
    }

    setLoading(true);
    try {
      await adminAPI.post(`/admin/database/schema/tables/${selectedTable.name}/fields`, newField);
      setSuccessMessage(`Field "${newField.field_name}" added successfully to table "${selectedTable.name}"`);
      setShowSuccessModal(true);
      setShowAddFieldModal(false);
      setNewField({
        field_name: '',
        field_type: 'TEXT',
        is_nullable: true,
        is_unique: false,
        default_value: ''
      });
      await loadTablesWithSchema();
    } catch (error) {
      setErrorMessage('Failed to add field: ' + (error.response?.data?.error || error.message));
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTable = async () => {
    if (!newTable.table_name || newTable.fields.length === 0) {
      setErrorMessage('Please provide table name and at least one field');
      setShowErrorModal(true);
      return;
    }

    setLoading(true);
    try {
      await adminAPI.post('/admin/database/schema/tables', newTable);
      setSuccessMessage(`Table "${newTable.table_name}" created successfully`);
      setShowSuccessModal(true);
      setShowCreateTableModal(false);
      setNewTable({
        table_name: '',
        fields: [
          {
            name: 'id',
            type: 'INTEGER',
            is_primary_key: true,
            is_nullable: false,
            is_unique: false,
            default_value: ''
          }
        ]
      });
      await loadTablesWithSchema();
    } catch (error) {
      setErrorMessage('Failed to create table: ' + (error.response?.data?.error || error.message));
      setShowErrorModal(true);
    } finally {
      setLoading(false);
    }
  };

  const addFieldToNewTable = () => {
    setNewTable(prev => ({
      ...prev,
      fields: [
        ...prev.fields,
        {
          name: '',
          type: 'TEXT',
          is_primary_key: false,
          is_nullable: true,
          is_unique: false,
          default_value: ''
        }
      ]
    }));
  };

  const removeFieldFromNewTable = (index) => {
    if (newTable.fields.length > 1) {
      setNewTable(prev => ({
        ...prev,
        fields: prev.fields.filter((_, i) => i !== index)
      }));
    }
  };

  const updateNewTableField = (index, field, value) => {
    setNewTable(prev => ({
      ...prev,
      fields: prev.fields.map((f, i) => 
        i === index ? { ...f, [field]: value } : f
      )
    }));
  };

  const getDataTypeColor = (type) => {
    const typeColors = {
      'INTEGER': 'primary',
      'TEXT': 'success',
      'REAL': 'warning',
      'BLOB': 'secondary',
      'BOOLEAN': 'info',
      'DATE': 'dark',
      'DATETIME': 'dark',
      'TIMESTAMP': 'dark'
    };
    return typeColors[type.toUpperCase()] || 'secondary';
  };

  if (loading && tables.length === 0) {
    return (
      <div className="text-center my-5">
        <Spinner animation="border" variant="primary" />
        <p className="mt-2">Loading database schema...</p>
      </div>
    );
  }

  return (
    <div className="database-configuration">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h4 className="mb-0">
          <FontAwesomeIcon icon={faDatabase} className="me-2" />
          Database Configuration
        </h4>
        <ButtonGroup>
          <Button 
            variant="success" 
            size="sm"
            onClick={() => setShowCreateTableModal(true)}
          >
            <FontAwesomeIcon icon={faPlus} className="me-2" />
            Create Table
          </Button>
          <Button 
            variant="outline-primary" 
            size="sm"
            onClick={loadTablesWithSchema}
            disabled={loading}
          >
            <FontAwesomeIcon icon={loading ? faSpinner : faDatabase} spin={loading} className="me-2" />
            Refresh
          </Button>
        </ButtonGroup>
      </div>

      <Tabs
        activeKey={activeTab}
        onSelect={(k) => setActiveTab(k)}
        className="mb-4"
      >
        <Tab 
          eventKey="schema-viewer" 
          title={
            <>
              <FontAwesomeIcon icon={faEye} className="me-2" />
              Schema Viewer
            </>
          }
        >
          <Row>
            <Col md={4}>
              <Card className="h-100">
                <Card.Header>
                  <h6 className="mb-0">
                    <FontAwesomeIcon icon={faTable} className="me-2" />
                    Database Tables ({tables.length})
                  </h6>
                </Card.Header>
                <Card.Body className="p-0">
                  {tables.length === 0 ? (
                    <div className="text-center p-4 text-muted">
                      <FontAwesomeIcon icon={faInfoCircle} size="2x" className="mb-2" />
                      <p>No tables found</p>
                    </div>
                  ) : (
                    <div className="list-group list-group-flush">
                      {tables.map((table) => (
                        <button
                          key={table.name}
                          className={`list-group-item list-group-item-action d-flex justify-content-between align-items-center ${
                            selectedTable?.name === table.name ? 'active' : ''
                          }`}
                          onClick={() => setSelectedTable(table)}
                        >
                          <div>
                            <strong>{table.name}</strong>
                            <br />
                            <small className="text-muted">
                              {table.columns.length} columns, {table.row_count} rows
                            </small>
                          </div>
                          <Badge bg="secondary">{table.columns.length}</Badge>
                        </button>
                      ))}
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Col>
            
            <Col md={8}>
              {selectedTable ? (
                <Card>
                  <Card.Header className="d-flex justify-content-between align-items-center">
                    <h6 className="mb-0">
                      <FontAwesomeIcon icon={faColumns} className="me-2" />
                      Table: {selectedTable.name}
                    </h6>
                    <Button 
                      variant="outline-success" 
                      size="sm"
                      onClick={() => setShowAddFieldModal(true)}
                    >
                      <FontAwesomeIcon icon={faPlus} className="me-2" />
                      Add Field
                    </Button>
                  </Card.Header>
                  <Card.Body>
                    <Table responsive striped>
                      <thead>
                        <tr>
                          <th>Field Name</th>
                          <th>Data Type</th>
                          <th>Nullable</th>
                          <th>Default</th>
                          <th>Primary Key</th>
                        </tr>
                      </thead>
                      <tbody>
                        {selectedTable.columns.map((column, index) => (
                          <tr key={index}>
                            <td>
                              <strong>{column.name}</strong>
                            </td>
                            <td>
                              <Badge bg={getDataTypeColor(column.type)}>
                                {column.type}
                              </Badge>
                            </td>
                            <td>
                              {column.notnull === 0 ? (
                                <Badge bg="success">Yes</Badge>
                              ) : (
                                <Badge bg="danger">No</Badge>
                              )}
                            </td>
                            <td>
                              <code>{column.dflt_value || 'NULL'}</code>
                            </td>
                            <td>
                              {column.pk === 1 ? (
                                <Badge bg="warning">
                                  <FontAwesomeIcon icon={faCheckCircle} className="me-1" />
                                  Yes
                                </Badge>
                              ) : (
                                <span className="text-muted">No</span>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </Card.Body>
                </Card>
              ) : (
                <Card>
                  <Card.Body className="text-center text-muted">
                    <FontAwesomeIcon icon={faTable} size="3x" className="mb-3" />
                    <h5>Select a table to view its schema</h5>
                    <p>Choose a table from the list on the left to see its structure and fields.</p>
                  </Card.Body>
                </Card>
              )}
            </Col>
          </Row>
        </Tab>

        <Tab
          eventKey="data-types"
          title={
            <>
              <FontAwesomeIcon icon={faCode} className="me-2" />
              Data Types Reference
            </>
          }
        >
          <Card>
            <Card.Header>
              <h6 className="mb-0">
                <FontAwesomeIcon icon={faInfoCircle} className="me-2" />
                Supported SQLite Data Types
              </h6>
            </Card.Header>
            <Card.Body>
              <Row>
                {dataTypes.map((dataType, index) => (
                  <Col md={6} key={index} className="mb-3">
                    <Card className="h-100">
                      <Card.Body>
                        <h6 className="card-title">
                          <Badge bg={getDataTypeColor(dataType.name)} className="me-2">
                            {dataType.name}
                          </Badge>
                        </h6>
                        <p className="card-text small">{dataType.description}</p>
                        <div className="mt-2">
                          <strong className="small">Examples:</strong>
                          <ul className="small mb-0 mt-1">
                            {dataType.examples.map((example, i) => (
                              <li key={i}><code>{example}</code></li>
                            ))}
                          </ul>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Card.Body>
          </Card>
        </Tab>
      </Tabs>

      {/* Add Field Modal */}
      <Modal show={showAddFieldModal} onHide={() => setShowAddFieldModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <FontAwesomeIcon icon={faPlus} className="me-2" />
            Add New Field to {selectedTable?.name}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={6}>
                <TextInput
                  name="field_name"
                  label="Field Name"
                  value={newField.field_name}
                  onChange={(e) => setNewField(prev => ({ ...prev, field_name: e.target.value }))}
                  placeholder="Enter field name"
                  required
                />
              </Col>
              <Col md={6}>
                <SelectInput
                  name="field_type"
                  label="Data Type"
                  value={newField.field_type}
                  onChange={(e) => setNewField(prev => ({ ...prev, field_type: e.target.value }))}
                  options={dataTypes.map(dt => ({ value: dt.name, label: dt.name }))}
                  required
                />
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Field Properties</Form.Label>
                  <div>
                    <Form.Check
                      type="switch"
                      id="is_nullable"
                      label="Allow NULL values"
                      checked={newField.is_nullable}
                      onChange={(e) => setNewField(prev => ({ ...prev, is_nullable: e.target.checked }))}
                      className="mb-2"
                    />
                    <Form.Check
                      type="switch"
                      id="is_unique"
                      label="Unique constraint"
                      checked={newField.is_unique}
                      onChange={(e) => setNewField(prev => ({ ...prev, is_unique: e.target.checked }))}
                    />
                  </div>
                </Form.Group>
              </Col>
              <Col md={6}>
                <TextInput
                  name="default_value"
                  label="Default Value (Optional)"
                  value={newField.default_value}
                  onChange={(e) => setNewField(prev => ({ ...prev, default_value: e.target.value }))}
                  placeholder="Enter default value"
                />
              </Col>
            </Row>

            {!newField.is_nullable && !newField.default_value && (
              <Alert variant="warning">
                <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
                <strong>Warning:</strong> Non-nullable fields require a default value when adding to existing tables.
              </Alert>
            )}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowAddFieldModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleAddField}
            disabled={loading || !newField.field_name || !newField.field_type}
          >
            {loading ? (
              <>
                <FontAwesomeIcon icon={faSpinner} spin className="me-2" />
                Adding...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faSave} className="me-2" />
                Add Field
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Create Table Modal */}
      <Modal show={showCreateTableModal} onHide={() => setShowCreateTableModal(false)} size="xl">
        <Modal.Header closeButton>
          <Modal.Title>
            <FontAwesomeIcon icon={faTable} className="me-2" />
            Create New Table
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row className="mb-4">
              <Col md={6}>
                <TextInput
                  name="table_name"
                  label="Table Name"
                  value={newTable.table_name}
                  onChange={(e) => setNewTable(prev => ({ ...prev, table_name: e.target.value }))}
                  placeholder="Enter table name"
                  required
                />
              </Col>
            </Row>

            <div className="d-flex justify-content-between align-items-center mb-3">
              <h6 className="mb-0">Table Fields</h6>
              <Button variant="outline-success" size="sm" onClick={addFieldToNewTable}>
                <FontAwesomeIcon icon={faPlus} className="me-2" />
                Add Field
              </Button>
            </div>

            {newTable.fields.map((field, index) => (
              <Card key={index} className="mb-3">
                <Card.Body>
                  <Row>
                    <Col md={3}>
                      <TextInput
                        name={`field_name_${index}`}
                        label="Field Name"
                        value={field.name}
                        onChange={(e) => updateNewTableField(index, 'name', e.target.value)}
                        placeholder="Field name"
                        required
                      />
                    </Col>
                    <Col md={2}>
                      <SelectInput
                        name={`field_type_${index}`}
                        label="Data Type"
                        value={field.type}
                        onChange={(e) => updateNewTableField(index, 'type', e.target.value)}
                        options={dataTypes.map(dt => ({ value: dt.name, label: dt.name }))}
                        required
                      />
                    </Col>
                    <Col md={2}>
                      <Form.Group className="mb-3">
                        <Form.Label>Properties</Form.Label>
                        <div>
                          <Form.Check
                            type="switch"
                            id={`primary_key_${index}`}
                            label="Primary Key"
                            checked={field.is_primary_key}
                            onChange={(e) => updateNewTableField(index, 'is_primary_key', e.target.checked)}
                            size="sm"
                          />
                        </div>
                      </Form.Group>
                    </Col>
                    <Col md={2}>
                      <Form.Group className="mb-3">
                        <Form.Label>&nbsp;</Form.Label>
                        <div>
                          <Form.Check
                            type="switch"
                            id={`nullable_${index}`}
                            label="Nullable"
                            checked={field.is_nullable}
                            onChange={(e) => updateNewTableField(index, 'is_nullable', e.target.checked)}
                            disabled={field.is_primary_key}
                            size="sm"
                          />
                        </div>
                      </Form.Group>
                    </Col>
                    <Col md={2}>
                      <Form.Group className="mb-3">
                        <Form.Label>&nbsp;</Form.Label>
                        <div>
                          <Form.Check
                            type="switch"
                            id={`unique_${index}`}
                            label="Unique"
                            checked={field.is_unique}
                            onChange={(e) => updateNewTableField(index, 'is_unique', e.target.checked)}
                            disabled={field.is_primary_key}
                            size="sm"
                          />
                        </div>
                      </Form.Group>
                    </Col>
                    <Col md={1}>
                      <Form.Group className="mb-3">
                        <Form.Label>&nbsp;</Form.Label>
                        <div>
                          <Button
                            variant="outline-danger"
                            size="sm"
                            onClick={() => removeFieldFromNewTable(index)}
                            disabled={newTable.fields.length === 1}
                          >
                            <FontAwesomeIcon icon={faTrash} />
                          </Button>
                        </div>
                      </Form.Group>
                    </Col>
                  </Row>
                  {!field.is_primary_key && (
                    <Row>
                      <Col md={6}>
                        <TextInput
                          name={`default_value_${index}`}
                          label="Default Value (Optional)"
                          value={field.default_value}
                          onChange={(e) => updateNewTableField(index, 'default_value', e.target.value)}
                          placeholder="Enter default value"
                        />
                      </Col>
                    </Row>
                  )}
                </Card.Body>
              </Card>
            ))}
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowCreateTableModal(false)}>
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleCreateTable}
            disabled={loading || !newTable.table_name || newTable.fields.some(f => !f.name || !f.type)}
          >
            {loading ? (
              <>
                <FontAwesomeIcon icon={faSpinner} spin className="me-2" />
                Creating...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faSave} className="me-2" />
                Create Table
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Success Modal */}
      <SuccessModal
        show={showSuccessModal}
        onHide={() => setShowSuccessModal(false)}
        title="Success"
        message={successMessage}
      />

      {/* Error Modal */}
      <ErrorModal
        show={showErrorModal}
        onHide={() => setShowErrorModal(false)}
        title="Error"
        message={errorMessage}
      />
    </div>
  );
};

export default DatabaseConfiguration;
