#!/usr/bin/env python3
"""
Debug script to test the exact users API endpoint
"""
import requests
import json

def test_users_api():
    """Test the users API endpoint with detailed debugging"""
    print("Testing Users API with debugging...")
    
    # Login first
    login_data = {
        "username": "soorya",
        "password": "12345678"
    }
    
    try:
        # Login
        response = requests.post(
            "http://localhost:5002/api/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"✗ Login failed: {response.text}")
            return
            
        data = response.json()
        token = data.get('token')
        user = data.get('user')
        
        print("✓ Login successful")
        print(f"  User Role: {user.get('role')}")
        print(f"  User ID: {user.get('id')}")
        
        # Test different possible URLs for the users endpoint
        headers = {"Authorization": f"Bearer {token}"}
        
        urls_to_test = [
            "http://localhost:5002/api/admin/users",
            "http://localhost:5002/admin/users",
            "http://localhost:5002/api/users"
        ]
        
        for url in urls_to_test:
            print(f"\nTesting URL: {url}")
            
            try:
                users_response = requests.get(url, headers=headers)
                print(f"  Status: {users_response.status_code}")
                
                if users_response.status_code == 200:
                    users_data = users_response.json()
                    print(f"  ✓ Success! Found {len(users_data)} users")
                    
                    # Check if soorya is in the list
                    soorya_in_list = any(u.get('username') == 'soorya' for u in users_data)
                    if soorya_in_list:
                        print("  ✓ Soorya user found in list")
                    else:
                        print("  ✗ Soorya user NOT in list")
                    break
                else:
                    print(f"  ✗ Failed: {users_response.text}")
                    
            except Exception as e:
                print(f"  ✗ Error: {str(e)}")
        
        # Also test if we can access other admin endpoints
        print(f"\nTesting other admin endpoints...")
        
        other_endpoints = [
            "/api/admin/analytics",
            "/api/admin/master-data"
        ]
        
        for endpoint in other_endpoints:
            url = f"http://localhost:5002{endpoint}"
            try:
                resp = requests.get(url, headers=headers)
                print(f"  {endpoint}: {resp.status_code}")
            except Exception as e:
                print(f"  {endpoint}: Error - {str(e)}")
            
    except Exception as e:
        print(f"✗ Error: {str(e)}")

if __name__ == "__main__":
    test_users_api()
