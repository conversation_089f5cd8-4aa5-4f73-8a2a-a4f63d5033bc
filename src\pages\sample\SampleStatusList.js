import React, { useEffect, useState } from "react";
import axios from "axios";

export default function SampleStatusList() {
  const [reports, setReports] = useState([]);
  const [filters, setFilters] = useState({
    date: "",
    patient_name: "",
    sid_number: ""
  });
  const [loading, setLoading] = useState(false);

  const fetchReports = async () => {
    try {
      setLoading(true);
      const params = {};
      if (filters.date) params.date = filters.date;
      if (filters.patient_name) params.patient_name = filters.patient_name;
      if (filters.sid_number) params.sid_number = filters.sid_number;

      const res = await axios.get("http://localhost:5002/api/billing-reports/sample-status", {
        params,
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`
        }
      });

      // Some APIs wrap inside `data.data`
      setReports(res.data.data || res.data || []);
    } catch (err) {
      console.error("Error fetching sample status:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReports();
  }, []);

  return (
    <div className="container mt-4">
      {/* Filters */}
      <div className="card shadow-sm mb-4">
        <div className="card-body">
          <div className="row g-3 align-items-end">
            <div className="col-md-3">
              <label className="form-label">Date</label>
              <input
                type="date"
                className="form-control"
                value={filters.date}
                onChange={(e) => setFilters({ ...filters, date: e.target.value })}
              />
            </div>
            <div className="col-md-3">
              <label className="form-label">Patient Name</label>
              <input
                type="text"
                className="form-control"
                placeholder="Search by patient"
                value={filters.patient_name}
                onChange={(e) => setFilters({ ...filters, patient_name: e.target.value })}
              />
            </div>
            <div className="col-md-3">
              <label className="form-label">SID Number</label>
              <input
                type="text"
                className="form-control"
                placeholder="Search by SID"
                value={filters.sid_number}
                onChange={(e) => setFilters({ ...filters, sid_number: e.target.value })}
              />
            </div>
            <div className="col-md-3 d-flex gap-2">
              <button
                className="btn btn-primary w-100"
                onClick={fetchReports}
                disabled={loading}
              >
                {loading ? "Loading..." : "Apply Filters"}
              </button>
              <button
                className="btn btn-outline-secondary"
                onClick={() => {
                  setFilters({ date: "", patient_name: "", sid_number: "" });
                  fetchReports();
                }}
              >
                Reset
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="card shadow-sm">
        <div className="card-body">
          <div className="table-responsive">
            <table className="table table-striped table-hover align-middle">
              <thead className="table-dark">
                <tr>
                  <th>SID Number</th>
                  <th>Patient Name</th>
                  <th>Billing Date</th>
                  <th>Clinic</th>
                  <th>Tests & Sample Status</th>
                </tr>
              </thead>
              <tbody>
                {reports.length === 0 && (
                  <tr>
                    <td colSpan={5} className="text-center text-muted">
                      No records found
                    </td>
                  </tr>
                )}
                {reports.map((report) => (
                  <tr key={report.billing_id}>
                    <td><strong>{report.sid_number}</strong></td>
                    <td>{report.patient_name}</td>
                    <td>{report.billing_date}</td>
                    <td>{report.clinic_name}</td>
                    <td>
                      <table className="table table-sm mb-0">
                        <tbody>
                          {report.tests.map((test, idx) => (
                            <tr key={idx}>
                              <td>{test.test_name}</td>
                              <td>
                                <span
                                  className={`badge ${
                                    test.sample_status === "Received"
                                      ? "bg-success"
                                      : "bg-danger"
                                  }`}
                                >
                                  {test.sample_status}
                                </span>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
