import api from './api';

const adminAPI = {
  // Database Tables
  getTables: () => api.get('/admin/database/tables'),
  
  getTableSchema: (tableName) => api.get(`/admin/database/tables/${tableName}/schema`),
  
  getTableData: (tableName, params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/${tableName}/data?${queryParams}`);
  },
  
  createRecord: (tableName, data) => api.post(`/admin/database/tables/${tableName}/records`, data),
  
  updateRecord: (tableName, recordId, data) => api.put(`/admin/database/tables/${tableName}/records/${recordId}`, data),
  
  deleteRecord: (tableName, recordId) => api.delete(`/admin/database/tables/${tableName}/records/${recordId}`),
  
  // Database Statistics
  getDatabaseStats: () => api.get('/admin/database/stats'),
  
  // Database Tools
  backupDatabase: () => api.post('/admin/database/backup'),
  
  optimizeDatabase: () => api.post('/admin/database/optimize'),
  
  executeQuery: (query) => api.post('/admin/database/query', { query }),
  
  // Import/Export
  exportTable: (tableName) => api.get(`/admin/database/export/${tableName}`),

  importTable: (tableName, records) => api.post(`/admin/database/import/${tableName}`, { records }),

  // Database Configuration
  testDatabaseConnection: (config) => api.post('/admin/database/test-connection', config),

  getDatabaseConfig: () => api.get('/admin/database/config'),

  updateDatabaseConfig: (config) => api.post('/admin/database/config', config),

  // Enhanced Database Schema Management
  getTableFields: (tableName) => api.get(`/admin/database/tables/${tableName}/fields`),

  addTableField: (tableName, fieldData) => api.post(`/admin/database/tables/${tableName}/fields`, fieldData),

  deleteTableField: (tableName, fieldName, force = false) =>
    api.delete(`/admin/database/tables/${tableName}/fields/${fieldName}?force=${force}`),

  // Settings Management
  getSettings: () => api.get('/admin/settings'),

  updateSettings: (settings) => api.put('/admin/settings', settings),
  
  // Specific table operations
  
  // Patients
  getPatients: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/patients/data?${queryParams}`);
  },
  
  createPatient: (data) => api.post('/admin/database/tables/patients/records', data),
  
  updatePatient: (id, data) => api.put(`/admin/database/tables/patients/records/${id}`, data),
  
  deletePatient: (id) => api.delete(`/admin/database/tables/patients/records/${id}`),
  
  // Users
  getUsers: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/users/data?${queryParams}`);
  },
  
  createUser: (data) => api.post('/admin/database/tables/users/records', data),
  
  updateUser: (id, data) => api.put(`/admin/database/tables/users/records/${id}`, data),
  
  deleteUser: (id) => api.delete(`/admin/database/tables/users/records/${id}`),
  
  // Tenants
  getTenants: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/tenants/data?${queryParams}`);
  },
  
  createTenant: (data) => api.post('/admin/database/tables/tenants/records', data),
  
  updateTenant: (id, data) => api.put(`/admin/database/tables/tenants/records/${id}`, data),
  
  deleteTenant: (id) => api.delete(`/admin/database/tables/tenants/records/${id}`),
  
  // Test Master
  getTestMaster: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/test_master/data?${queryParams}`);
  },
  
  createTest: (data) => api.post('/admin/database/tables/test_master/records', data),
  
  updateTest: (id, data) => api.put(`/admin/database/tables/test_master/records/${id}`, data),
  
  deleteTest: (id) => api.delete(`/admin/database/tables/test_master/records/${id}`),
  
  // Departments
  getDepartments: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/departments/data?${queryParams}`);
  },
  
  createDepartment: (data) => api.post('/admin/database/tables/departments/records', data),
  
  updateDepartment: (id, data) => api.put(`/admin/database/tables/departments/records/${id}`, data),
  
  deleteDepartment: (id) => api.delete(`/admin/database/tables/departments/records/${id}`),
  
  // Samples
  getSamples: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/samples/data?${queryParams}`);
  },
  
  createSample: (data) => api.post('/admin/database/tables/samples/records', data),
  
  updateSample: (id, data) => api.put(`/admin/database/tables/samples/records/${id}`, data),
  
  deleteSample: (id) => api.delete(`/admin/database/tables/samples/records/${id}`),
  
  // Billings
  getBillings: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/billings/data?${queryParams}`);
  },
  
  createBilling: (data) => api.post('/admin/database/tables/billings/records', data),
  
  updateBilling: (id, data) => api.put(`/admin/database/tables/billings/records/${id}`, data),
  
  deleteBilling: (id) => api.delete(`/admin/database/tables/billings/records/${id}`),
  
  // Results
  getResults: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/results/data?${queryParams}`);
  },
  
  createResult: (data) => api.post('/admin/database/tables/results/records', data),
  
  updateResult: (id, data) => api.put(`/admin/database/tables/results/records/${id}`, data),
  
  deleteResult: (id) => api.delete(`/admin/database/tables/results/records/${id}`),
  
  // Master Data Tables
  
  // Sample Types
  getSampleTypes: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/sample_types/data?${queryParams}`);
  },
  
  createSampleType: (data) => api.post('/admin/database/tables/sample_types/records', data),
  
  updateSampleType: (id, data) => api.put(`/admin/database/tables/sample_types/records/${id}`, data),
  
  deleteSampleType: (id) => api.delete(`/admin/database/tables/sample_types/records/${id}`),
  
  // Containers
  getContainers: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/containers/data?${queryParams}`);
  },
  
  createContainer: (data) => api.post('/admin/database/tables/containers/records', data),
  
  updateContainer: (id, data) => api.put(`/admin/database/tables/containers/records/${id}`, data),
  
  deleteContainer: (id) => api.delete(`/admin/database/tables/containers/records/${id}`),
  
  // Test Categories
  getTestCategories: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/test_categories/data?${queryParams}`);
  },
  
  createTestCategory: (data) => api.post('/admin/database/tables/test_categories/records', data),
  
  updateTestCategory: (id, data) => api.put(`/admin/database/tables/test_categories/records/${id}`, data),
  
  deleteTestCategory: (id) => api.delete(`/admin/database/tables/test_categories/records/${id}`),
  
  // Payment Methods
  getPaymentMethods: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/payment_methods/data?${queryParams}`);
  },
  
  createPaymentMethod: (data) => api.post('/admin/database/tables/payment_methods/records', data),
  
  updatePaymentMethod: (id, data) => api.put(`/admin/database/tables/payment_methods/records/${id}`, data),
  
  deletePaymentMethod: (id) => api.delete(`/admin/database/tables/payment_methods/records/${id}`),
  
  // Roles
  getRoles: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/roles/data?${queryParams}`);
  },
  
  createRole: (data) => api.post('/admin/database/tables/roles/records', data),
  
  updateRole: (id, data) => api.put(`/admin/database/tables/roles/records/${id}`, data),
  
  deleteRole: (id) => api.delete(`/admin/database/tables/roles/records/${id}`),
  
  // Permissions
  getPermissions: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/permissions/data?${queryParams}`);
  },
  
  createPermission: (data) => api.post('/admin/database/tables/permissions/records', data),
  
  updatePermission: (id, data) => api.put(`/admin/database/tables/permissions/records/${id}`, data),
  
  deletePermission: (id) => api.delete(`/admin/database/tables/permissions/records/${id}`),
  
  // Inventory
  getInventory: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/inventory/data?${queryParams}`);
  },
  
  createInventoryItem: (data) => api.post('/admin/database/tables/inventory/records', data),
  
  updateInventoryItem: (id, data) => api.put(`/admin/database/tables/inventory/records/${id}`, data),
  
  deleteInventoryItem: (id) => api.delete(`/admin/database/tables/inventory/records/${id}`),
  
  // Instruments
  getInstruments: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/instruments/data?${queryParams}`);
  },
  
  createInstrument: (data) => api.post('/admin/database/tables/instruments/records', data),
  
  updateInstrument: (id, data) => api.put(`/admin/database/tables/instruments/records/${id}`, data),
  
  deleteInstrument: (id) => api.delete(`/admin/database/tables/instruments/records/${id}`),
  
  // Reagents
  getReagents: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/reagents/data?${queryParams}`);
  },
  
  createReagent: (data) => api.post('/admin/database/tables/reagents/records', data),
  
  updateReagent: (id, data) => api.put(`/admin/database/tables/reagents/records/${id}`, data),
  
  deleteReagent: (id) => api.delete(`/admin/database/tables/reagents/records/${id}`),
  
  // Suppliers
  getSuppliers: (params = {}) => {
    const queryParams = new URLSearchParams(params).toString();
    return api.get(`/admin/database/tables/suppliers/data?${queryParams}`);
  },
  
  createSupplier: (data) => api.post('/admin/database/tables/suppliers/records', data),
  
  updateSupplier: (id, data) => api.put(`/admin/database/tables/suppliers/records/${id}`, data),
  
  deleteSupplier: (id) => api.delete(`/admin/database/tables/suppliers/records/${id}`)
};

export { adminAPI };
export default adminAPI;
