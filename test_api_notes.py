#!/usr/bin/env python3
"""
Test the billing reports API to verify notes field is included
"""

import requests
import json
import sys

def get_auth_token():
    """Get authentication token"""
    try:
        login_url = "http://localhost:5002/api/auth/login"
        login_data = {"username": "admin", "password": "admin123"}
        login_response = requests.post(login_url, json=login_data, timeout=10)

        if login_response.status_code == 200:
            return login_response.json().get('token')
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return None

def test_api_notes():
    """Test the API endpoint for notes field"""

    # Get authentication token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return

    # Test the API endpoint
    base_url = 'http://localhost:5002'
    sid = 'MYD098'
    url = f'{base_url}/api/billing-reports/sid/{sid}'

    # Headers with proper authentication
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print(f"🔍 Testing API endpoint: {url}")
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Response received")
            print(f"Response structure: {list(data.keys())}")
            
            # Navigate the nested response structure
            report_data = data.get('data', {})
            if isinstance(report_data, dict) and 'data' in report_data:
                report = report_data['data']
            else:
                report = report_data
            
            print(f"Report type: {type(report)}")
            
            if isinstance(report, dict):
                # Check for notes field in the response
                has_notes = 'notes' in report
                notes_value = report.get('notes', 'NOT_FOUND')
                
                print(f"\n📝 NOTES FIELD ANALYSIS:")
                print(f"  Has notes field: {has_notes}")
                print(f"  Notes value: \"{notes_value}\"")
                print(f"  Notes type: {type(notes_value)}")
                
                # Show top-level keys
                print(f"\n🔑 Report top-level keys:")
                for key in sorted(report.keys()):
                    print(f"  - {key}")
                
                # Check financial summary
                if 'financial_summary' in report:
                    fs = report['financial_summary']
                    print(f"\n💰 Financial Summary keys: {list(fs.keys())}")
                    
                # Check if GST fields are removed
                gst_fields = ['gst_rate', 'gst_amount']
                gst_found = []
                for field in gst_fields:
                    if field in report.get('financial_summary', {}):
                        gst_found.append(field)
                
                if gst_found:
                    print(f"⚠️  GST fields still present: {gst_found}")
                else:
                    print(f"✅ GST fields properly removed")
                    
            else:
                print(f"❌ Report data is not a dictionary: {report}")
                
        elif response.status_code == 404:
            print(f"❌ Report not found for SID: {sid}")
        elif response.status_code == 401:
            print(f"❌ Authentication failed - check token")
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection failed - backend server not running on {base_url}")
    except requests.exceptions.Timeout:
        print(f"❌ Request timeout")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_multiple_sids():
    """Test multiple SIDs to see notes field behavior"""

    # Get authentication token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return

    sids_to_test = ['MYD098', 'MYD001', 'TNJ004', 'MYD005']
    base_url = 'http://localhost:5002'

    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print(f"\n🔍 Testing multiple SIDs for notes field:")
    
    for sid in sids_to_test:
        url = f'{base_url}/api/billing-reports/sid/{sid}'
        
        try:
            response = requests.get(url, headers=headers, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                report_data = data.get('data', {})
                if isinstance(report_data, dict) and 'data' in report_data:
                    report = report_data['data']
                else:
                    report = report_data
                
                if isinstance(report, dict):
                    has_notes = 'notes' in report
                    notes_value = report.get('notes', 'NOT_FOUND')
                    print(f"  {sid}: has_notes={has_notes}, value=\"{notes_value}\"")
                else:
                    print(f"  {sid}: ❌ Invalid report data")
            else:
                print(f"  {sid}: ❌ Status {response.status_code}")
                
        except Exception as e:
            print(f"  {sid}: ❌ Error - {str(e)}")

if __name__ == "__main__":
    test_api_notes()
    test_multiple_sids()
