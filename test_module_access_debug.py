#!/usr/bin/env python3
"""
Debug script to test module access functionality
"""
import sys
import os
sys.path.append(os.path.join(os.getcwd(), 'backend'))

from utils import check_module_access, read_data

def test_module_access():
    """Test module access for developer user"""
    print("Testing module access for developer user...")
    
    # Get the developer user from database
    try:
        from database_manager import db_manager
        users = db_manager.get_all('users')
        soorya_user = next((u for u in users if u.get('username') == 'soorya'), None)
        
        if not soorya_user:
            print("✗ Soorya user not found in database")
            return
            
        print(f"✓ Found user: {soorya_user['username']} (role: {soorya_user['role']})")
        
        # Test module access
        has_access = check_module_access(soorya_user, 'USER_MANAGEMENT')
        print(f"Module access result: {has_access}")
        
        if has_access:
            print("✓ Developer user should have access to USER_MANAGEMENT module")
        else:
            print("✗ Developer user does NOT have access to USER_MANAGEMENT module")
            
        # Test with admin user for comparison
        admin_user = next((u for u in users if u.get('role') == 'admin'), None)
        if admin_user:
            admin_access = check_module_access(admin_user, 'USER_MANAGEMENT')
            print(f"Admin access result: {admin_access}")
            
        # Check modules.json
        modules = read_data('modules.json')
        user_mgmt_module = next((m for m in modules if m['code'] == 'USER_MANAGEMENT'), None)
        if user_mgmt_module:
            print(f"✓ USER_MANAGEMENT module found: ID {user_mgmt_module['id']}")
        else:
            print("✗ USER_MANAGEMENT module not found in modules.json")
            
    except Exception as e:
        print(f"✗ Error: {str(e)}")

if __name__ == "__main__":
    test_module_access()
