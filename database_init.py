#!/usr/bin/env python3
"""
Database Initialization Script for AVINI Labs
Creates SQLite database with all tables, indexes, and initial data
"""

import sqlite3
import os
import json
from datetime import datetime

class DatabaseInitializer:
    def __init__(self, db_path='backend/data/avini_labs.db'):
        self.db_path = db_path
        self.schema_file = 'database_schema_design.sql'
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    def create_database(self):
        """Create the database and execute schema"""
        print(f"Creating database at: {self.db_path}")
        
        # Remove existing database if it exists
        if os.path.exists(self.db_path):
            backup_path = f"{self.db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            os.rename(self.db_path, backup_path)
            print(f"Existing database backed up to: {backup_path}")
        
        # Create new database
        conn = sqlite3.connect(self.db_path)
        
        try:
            # Read and execute schema
            with open(self.schema_file, 'r') as f:
                schema_sql = f.read()
            
            # Execute schema in chunks (SQLite doesn't support multiple statements well)
            statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
            
            for statement in statements:
                if statement:
                    try:
                        conn.execute(statement)
                        print(f"✓ Executed: {statement[:50]}...")
                    except Exception as e:
                        print(f"✗ Error executing statement: {statement[:50]}...")
                        print(f"  Error: {e}")
            
            conn.commit()
            print("✓ Database schema created successfully")
            
        except Exception as e:
            print(f"✗ Error creating database: {e}")
            conn.rollback()
            raise
        
        finally:
            conn.close()
    
    def insert_initial_data(self):
        """Insert initial/seed data"""
        print("Inserting initial data...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Insert initial tenant (main hub)
            cursor.execute("""
                INSERT INTO tenants (name, site_code, address, city, state, contact_phone, email, is_hub, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                'AVINI Labs Mayiladuthurai',
                'MYD',
                'Main Hub, Mayiladuthurai',
                'Mayiladuthurai',
                'Tamil Nadu',
                '6384440505',
                '<EMAIL>',
                True,
                True
            ))
            
            # Insert admin user
            cursor.execute("""
                INSERT INTO users (username, password, email, first_name, last_name, role, tenant_id, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                'admin',
                'admin123',  # In production, this should be hashed
                '<EMAIL>',
                'Admin',
                'User',
                'admin',
                1,
                True
            ))
            
            # Insert basic departments
            departments = [
                ('BIOCHEMISTRY', 'Biochemistry', 'Clinical biochemistry tests'),
                ('HAEMATOLOGY', 'Haematology', 'Blood and blood-related tests'),
                ('MICROBIOLOGY', 'Microbiology', 'Microbial analysis and culture'),
                ('IMMUNOLOGY', 'Immunology', 'Immunological and serological tests'),
                ('PATHOLOGY', 'Pathology', 'Histopathology and cytology'),
                ('ENDOCRINOLOGY', 'Endocrinology', 'Hormone and endocrine tests'),
                ('MOLECULAR', 'Molecular Biology', 'DNA/RNA and molecular diagnostics')
            ]
            
            for code, name, desc in departments:
                cursor.execute("""
                    INSERT INTO departments (code, name, description, is_active)
                    VALUES (?, ?, ?, ?)
                """, (code, name, desc, True))
            
            # Insert basic test categories
            categories = [
                ('ROUTINE', 'Routine Tests', 'Standard laboratory tests'),
                ('EMERGENCY', 'Emergency Tests', 'Urgent/STAT tests'),
                ('SPECIAL', 'Special Tests', 'Specialized diagnostic tests'),
                ('PROFILE', 'Test Profiles', 'Panel of multiple tests')
            ]
            
            for code, name, desc in categories:
                cursor.execute("""
                    INSERT INTO test_categories (code, name, description, is_active)
                    VALUES (?, ?, ?, ?)
                """, (code, name, desc, True))
            
            # Insert basic sample types
            sample_types = [
                ('BLOOD', 'Blood', 'Whole blood sample'),
                ('SERUM', 'Serum', 'Serum sample'),
                ('PLASMA', 'Plasma', 'Plasma sample'),
                ('URINE', 'Urine', 'Urine sample'),
                ('STOOL', 'Stool', 'Stool sample'),
                ('SPUTUM', 'Sputum', 'Sputum sample'),
                ('CSF', 'CSF', 'Cerebrospinal fluid'),
                ('TISSUE', 'Tissue', 'Tissue biopsy')
            ]
            
            for code, name, desc in sample_types:
                cursor.execute("""
                    INSERT INTO sample_types (type_code, type_name, description, is_active)
                    VALUES (?, ?, ?, ?)
                """, (code, name, desc, True))
            
            # Insert basic containers
            containers = [
                ('EDTA', 'EDTA Tube', 'Purple top tube with EDTA', '#800080', 5.0, 'EDTA'),
                ('SERUM', 'Serum Tube', 'Red top tube for serum', '#FF0000', 5.0, 'None'),
                ('FLUORIDE', 'Fluoride Tube', 'Gray top tube with fluoride', '#808080', 3.0, 'Sodium Fluoride'),
                ('CITRATE', 'Citrate Tube', 'Blue top tube with citrate', '#0000FF', 4.5, 'Sodium Citrate'),
                ('HEPARIN', 'Heparin Tube', 'Green top tube with heparin', '#008000', 4.0, 'Lithium Heparin')
            ]
            
            for code, name, desc, color, volume, additive in containers:
                cursor.execute("""
                    INSERT INTO containers (code, container_name, description, color_code, volume_required, additive, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (code, name, desc, color, volume, additive, True))
            
            # Insert basic payment methods
            payment_methods = [
                ('CASH', 'Cash', 'Cash payment', False, 0.0),
                ('CARD', 'Credit/Debit Card', 'Card payment', True, 2.5),
                ('UPI', 'UPI', 'UPI payment', True, 0.0),
                ('NEFT', 'NEFT', 'Bank transfer', True, 0.0),
                ('CHEQUE', 'Cheque', 'Cheque payment', False, 0.0)
            ]
            
            for code, name, desc, is_online, fee in payment_methods:
                cursor.execute("""
                    INSERT INTO payment_methods (method_code, method_name, description, is_online, processing_fee, is_active)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (code, name, desc, is_online, fee, True))
            
            # Insert basic roles
            roles = [
                ('ADMIN', 'Administrator', 'Full system access', '[]'),
                ('HUB_ADMIN', 'Hub Administrator', 'Hub management access', '[]'),
                ('FRANCHISE_ADMIN', 'Franchise Administrator', 'Franchise management access', '[]'),
                ('LAB_TECH', 'Lab Technician', 'Laboratory operations access', '[]'),
                ('RECEPTIONIST', 'Receptionist', 'Front desk operations access', '[]')
            ]
            
            for code, name, desc, permissions in roles:
                cursor.execute("""
                    INSERT INTO roles (code, name, description, permission_ids, is_active)
                    VALUES (?, ?, ?, ?, ?)
                """, (code, name, desc, permissions, True))
            
            # Insert basic permissions
            permissions = [
                ('PATIENT_READ', 'Read Patients', 'View patient information', 'patients'),
                ('PATIENT_WRITE', 'Write Patients', 'Create/update patient information', 'patients'),
                ('SAMPLE_READ', 'Read Samples', 'View sample information', 'samples'),
                ('SAMPLE_WRITE', 'Write Samples', 'Create/update sample information', 'samples'),
                ('BILLING_READ', 'Read Billing', 'View billing information', 'billing'),
                ('BILLING_WRITE', 'Write Billing', 'Create/update billing information', 'billing'),
                ('RESULT_READ', 'Read Results', 'View test results', 'results'),
                ('RESULT_WRITE', 'Write Results', 'Create/update test results', 'results'),
                ('ADMIN_READ', 'Read Admin', 'View admin information', 'admin'),
                ('ADMIN_WRITE', 'Write Admin', 'Create/update admin information', 'admin')
            ]
            
            for code, name, desc, module in permissions:
                cursor.execute("""
                    INSERT INTO permissions (code, name, description, module, is_active)
                    VALUES (?, ?, ?, ?, ?)
                """, (code, name, desc, module, True))
            
            conn.commit()
            print("✓ Initial data inserted successfully")
            
        except Exception as e:
            print(f"✗ Error inserting initial data: {e}")
            conn.rollback()
            raise
        
        finally:
            conn.close()
    
    def verify_database(self):
        """Verify database creation and data"""
        print("Verifying database...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Get table count
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            print(f"✓ Tables created: {table_count}")
            
            # Get index count
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='index'")
            index_count = cursor.fetchone()[0]
            print(f"✓ Indexes created: {index_count}")
            
            # Check data in key tables
            tables_to_check = ['tenants', 'users', 'departments', 'test_categories', 'sample_types', 'containers']
            
            for table in tables_to_check:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✓ {table}: {count} records")
            
            print("✓ Database verification completed successfully")
            
        except Exception as e:
            print(f"✗ Error verifying database: {e}")
            raise
        
        finally:
            conn.close()
    
    def initialize(self):
        """Complete database initialization"""
        print("Starting AVINI Labs Database Initialization")
        print("=" * 50)
        
        try:
            self.create_database()
            self.insert_initial_data()
            self.verify_database()
            
            print("\n" + "=" * 50)
            print("✓ Database initialization completed successfully!")
            print(f"Database location: {os.path.abspath(self.db_path)}")
            
        except Exception as e:
            print(f"\n✗ Database initialization failed: {e}")
            raise

def main():
    """Main function"""
    initializer = DatabaseInitializer()
    initializer.initialize()

if __name__ == "__main__":
    main()
