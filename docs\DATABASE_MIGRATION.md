# Database Migration System Documentation

## Overview

The Database Migration System provides a comprehensive solution for migrating data from JSON files to SQLite database, with support for multiple database types (SQLite, PostgreSQL, MySQL). The system includes a web-based interface for configuration, monitoring, and management.

## Features

- **Automated Data Migration**: Seamlessly migrate data from JSON files to SQLite database
- **Database Configuration UI**: Web interface for configuring database connections
- **Migration Status Dashboard**: Real-time monitoring of migration progress
- **Multiple Database Support**: SQLite, PostgreSQL, and MySQL support
- **Role-based Access Control**: Admin and hub_admin access only
- **Connection Testing**: Test database connections before migration
- **Backup Configuration**: Automated backup settings and management
- **Error Handling**: Comprehensive error reporting and logging

## Architecture

### Backend Components

1. **Migration Script** (`json_to_sqlite_migration.py`)
   - Core migration logic
   - Handles data type conversions
   - Creates database schema
   - Migrates data from JSON to SQLite

2. **Database Admin Routes** (`backend/routes/database_admin_routes.py`)
   - API endpoints for database configuration
   - Migration management endpoints
   - Connection testing functionality

3. **Admin Routes** (`backend/routes/admin_routes.py`)
   - Settings management endpoints
   - User authentication and authorization

### Frontend Components

1. **Database Configuration Tab** (`src/pages/admin/Settings.js`)
   - Database connection settings
   - Backup configuration
   - Connection testing interface

2. **Migration Dashboard** (`src/components/admin/MigrationDashboard.js`)
   - Real-time migration status
   - Progress monitoring
   - Log viewing capabilities

3. **API Services** (`src/services/adminAPI.js`)
   - Frontend API integration
   - Database configuration endpoints
   - Migration management calls

## Installation and Setup

### Prerequisites

- Python 3.8+
- Node.js 14+
- SQLite (included with Python)
- Optional: PostgreSQL or MySQL for advanced configurations

### Backend Setup

1. Install Python dependencies:
```bash
pip install flask sqlite3 psycopg2-binary mysql-connector-python
```

2. Ensure the migration script is executable:
```bash
chmod +x json_to_sqlite_migration.py
```

3. Create necessary directories:
```bash
mkdir -p data logs
```

### Frontend Setup

1. Install Node.js dependencies:
```bash
npm install react-bootstrap bootstrap @fortawesome/react-fontawesome
```

2. Ensure proper routing is configured for the migration dashboard

## Usage Guide

### 1. Database Configuration

#### Accessing the Configuration Interface

1. Navigate to **Admin > Settings**
2. Click on the **Database** tab
3. Configure your database settings:
   - **Database Type**: Choose SQLite, PostgreSQL, or MySQL
   - **Database Name**: Specify the database file/name
   - **Connection Details**: Host, port, username, password (for non-SQLite)
   - **Backup Settings**: Configure automatic backups

#### Testing Database Connection

1. Fill in the database configuration
2. Click **Test Connection**
3. Verify the connection status
4. Save settings if connection is successful

### 2. Running Data Migration

#### Using the Migration Dashboard

1. Navigate to **Admin > Migration Dashboard**
2. Review the current migration status
3. Click **Start Migration** to begin the process
4. Monitor progress in real-time
5. View detailed logs if needed

#### Manual Migration

Run the migration script directly:
```bash
python3 json_to_sqlite_migration.py
```

### 3. Monitoring and Troubleshooting

#### Migration Status

The dashboard provides:
- Overall migration progress
- Individual table status
- Error reporting
- Execution time tracking

#### Common Issues and Solutions

1. **Array/List Data Types**
   - Issue: SQLite doesn't support array types
   - Solution: Arrays are automatically converted to comma-separated strings

2. **Connection Failures**
   - Check database credentials
   - Verify network connectivity
   - Ensure database server is running

3. **Permission Errors**
   - Verify file permissions for SQLite database
   - Check user roles (admin/hub_admin required)

## API Reference

### Database Configuration Endpoints

#### Test Database Connection
```http
POST /admin/database/test-connection
Content-Type: application/json

{
  "db_type": "sqlite",
  "db_host": "localhost",
  "db_port": "5432",
  "db_name": "lab_management.db",
  "db_username": "",
  "db_password": ""
}
```

#### Get Database Configuration
```http
GET /admin/database/config
Authorization: Bearer <token>
```

#### Update Database Configuration
```http
POST /admin/database/config
Content-Type: application/json

{
  "db_type": "sqlite",
  "db_name": "avini_labs.db",
  "backup_enabled": true,
  "backup_frequency": "daily",
  "backup_retention_days": 30,
  "auto_optimize": true
}
```

### Migration Management Endpoints

#### Get Migration Status
```http
GET /admin/migration/status
Authorization: Bearer <token>
```

#### Start Migration
```http
POST /admin/migration/start
Authorization: Bearer <token>
```

#### Get Migration Logs
```http
GET /admin/migration/logs
Authorization: Bearer <token>
```

## Database Schema

### Migrated Tables

1. **tenants** - Tenant/organization information
2. **users** - User accounts and profiles
3. **patients** - Patient records
4. **departments** - Laboratory departments
5. **test_master** - Test definitions and configurations
6. **samples** - Sample tracking information
7. **billings** - Billing and invoice data
8. **results** - Test results and reports

### Data Type Mappings

| JSON Type | SQLite Type | Notes |
|-----------|-------------|-------|
| string | TEXT | Direct mapping |
| number | REAL/INTEGER | Based on value type |
| boolean | INTEGER | 0 for false, 1 for true |
| array | TEXT | Comma-separated values |
| object | TEXT | JSON string representation |
| null | NULL | Direct mapping |

## Security Considerations

### Access Control

- Database configuration requires admin or hub_admin role
- Migration operations are restricted to authorized users
- Database credentials are masked in API responses

### Data Protection

- Database passwords are not returned in API responses
- Migration logs may contain sensitive information
- Backup files should be stored securely

## Performance Optimization

### Migration Performance

- Large datasets are processed in batches
- Progress tracking minimizes memory usage
- Error handling prevents data corruption

### Database Optimization

- Auto-optimization can be enabled for maintenance
- Backup retention prevents disk space issues
- Connection pooling for better performance

## Troubleshooting

### Common Error Messages

1. **"Parameter X has an unsupported type"**
   - Cause: Array or object data in JSON
   - Solution: Data is automatically converted to strings

2. **"Database connection failed"**
   - Cause: Invalid credentials or network issues
   - Solution: Verify configuration and test connection

3. **"Migration already running"**
   - Cause: Another migration process is active
   - Solution: Wait for completion or check status

### Log Files

- Migration logs: `logs/migration.log`
- Application logs: Check Flask application logs
- Database logs: Refer to database-specific log files

## Support and Maintenance

### Regular Maintenance

1. Monitor backup files and disk space
2. Review migration logs for errors
3. Test database connections periodically
4. Update database drivers as needed

### Backup and Recovery

1. Configure automatic backups
2. Test backup restoration procedures
3. Store backups in secure locations
4. Document recovery procedures

## Future Enhancements

- Support for additional database types
- Advanced migration scheduling
- Data validation and integrity checks
- Migration rollback capabilities
- Performance monitoring and analytics
