{"name": "r<PERSON><PERSON>i", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "@mui/icons-material": "^7.3.1", "@mui/lab": "^7.0.0-beta.13", "@mui/material": "^7.1.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.4.0", "bootstrap": "^5.3.0", "bwip-js": "^4.6.0", "chart.js": "^4.3.0", "jsbarcode": "^3.11.6", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^3.1.2", "pdf-lib": "^1.17.1", "qrcode": "^1.5.4", "react": "^18.2.0", "react-bootstrap": "^2.7.4", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.13.0", "react-scripts": "5.0.1", "react-select": "^5.10.1", "react-to-print": "^3.1.0", "web-vitals": "^2.1.4", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "start:backend": "cd backend && python app.py", "start:dev": "./start.sh"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5002"}