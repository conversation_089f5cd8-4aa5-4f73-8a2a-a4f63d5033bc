{"timestamp": "2025-09-01T10:47:46.304161", "files_analyzed": 72, "total_records": 5385, "total_size_mb": 12.770182609558105, "file_analysis": {"antibiotic_master.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "antibiotic_code": "str", "antibiotic_group": "str", "antibiotic_description": "str", "antibiotic_content": "str", "order": "int", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0014705657958984375}, "audit_trail.json": {"type": "array", "count": 344, "sample_structure": {"id": "str", "timestamp": "str", "event_type": "str", "user_id": "int", "tenant_id": "int", "success": "bool", "details": "object", "ip_address": "str", "user_agent": "str"}, "file_size_mb": 0.135894775390625}, "authorization_settings.json": {"type": "array", "count": 3, "sample_structure": {"id": "int", "main": "str", "code": "str", "sub_name": "str", "service_time": "int", "authorization": "str", "authorization_type": "str", "email_at": "str", "report_type": "str", "specimen": "str", "staging": "str", "hide_sign": "bool", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0012998580932617188}, "billing.json": {"type": "array", "count": 61, "sample_structure": {"id": "int", "invoice_number": "str", "patient_id": "int", "items": "array[dict]", "subtotal": "int", "discount": "float", "tax": "float", "total_amount": "float", "paid_amount": "float", "balance": "int", "payment_method": "str", "payment_status": "str", "status": "str", "invoice_date": "str", "due_date": "str", "created_at": "str", "updated_at": "str", "tenant_id": "int", "created_by": "int", "sid_number": "str"}, "file_size_mb": 0.1000070571899414}, "billing_reports.json": {"type": "array", "count": 145, "sample_structure": {"id": "int", "sid_number": "str", "billing_id": "int", "patient_id": "int", "tenant_id": "int", "billing_date": "str", "due_date": "str", "generation_timestamp": "str", "report_version": "str", "patient_info": "object", "clinic_info": "object", "billing_header": "object", "test_items": "array[dict]", "unmatched_tests": "array[empty]", "financial_summary": "object", "metadata": "object", "notes": "str"}, "file_size_mb": 4.947537422180176}, "billings.json": {"type": "array", "count": 109, "sample_structure": {"id": "int", "invoice_number": "str", "patient_id": "int", "items": "array[dict]", "subtotal": "int", "discount": "float", "tax": "float", "total_amount": "float", "paid_amount": "float", "balance": "int", "payment_method": "str", "payment_status": "str", "status": "str", "invoice_date": "str", "due_date": "str", "created_at": "str", "updated_at": "str", "tenant_id": "int", "created_by": "int", "sid_number": "str"}, "file_size_mb": 0.5586156845092773}, "calculation_formulas.json": {"type": "list", "value": [], "file_size_mb": 2.86102294921875e-06}, "calibration_standards.json": {"type": "list", "value": [], "file_size_mb": 2.86102294921875e-06}, "container_master.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "code": "str", "description": "str", "short_name": "str", "color": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0011920928955078125}, "containers.json": {"type": "array", "count": 10, "sample_structure": {"id": "int", "container_name": "str", "color_code": "str", "sample_type_id": "int", "volume_required": "str", "additive": "str"}, "file_size_mb": 0.001697540283203125}, "department_settings.json": {"type": "array", "count": 3, "sample_structure": {"id": "int", "main": "str", "code": "str", "sub_name": "str", "service_time": "int", "room": "str", "order": "int", "dept_amt": "int", "short": "str", "collect": "str", "process_receive": "str", "receive": "str", "no": "str", "pending": "str", "dept": "str", "barcode": "str", "appt": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0013217926025390625}, "departments.json": {"type": "array", "count": 1004, "sample_structure": {"code": "str", "test_profile": "str", "department": "str", "test_price": "str", "id": "int", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.2872886657714844}, "doctors.json": {"type": "list", "value": [], "file_size_mb": 1.9073486328125e-06}, "error_log.json": {"type": "array", "count": 7, "sample_structure": {"id": "str", "timestamp": "str", "error_type": "str", "error_message": "str", "severity": "str", "context": "object", "user_id": "int", "tenant_id": "int", "resolved": "bool", "resolution_notes": "NoneType"}, "file_size_mb": 0.0033254623413085938}, "excel_test_data.json": {"type": "array", "count": 790, "sample_structure": {"id": "int", "test_name": "str", "test_code": "str", "department": "str", "reference_range": "NoneType", "result_unit": "NoneType", "decimals": "int", "critical_low": "NoneType", "critical_high": "NoneType", "price": "int", "result_type": "str", "short_name": "NoneType", "method_code": "NoneType", "method": "NoneType", "specimen_code": "NoneType", "specimen": "NoneType", "container_code": "NoneType", "container": "NoneType", "instructions": "NoneType", "notes": "NoneType", "min_sample_qty": "NoneType", "test_done_on": "str", "applicable_to": "str", "reporting_days": "int", "source_sheet": "str", "excel_source": "bool", "is_active": "bool", "created_at": "str", "updated_at": "str"}, "file_size_mb": 0.7031240463256836}, "franchise_permissions.json": {"type": "array", "count": 12, "sample_structure": {"id": "int", "franchise_id": "int", "franchise_name": "str", "module_permissions": "array[int]", "is_hub": "bool", "created_at": "str", "updated_at": "str", "created_by": "int", "updated_by": "int"}, "file_size_mb": 0.0041656494140625}, "gst_config.json": {"type": "array", "count": 3, "sample_structure": {"id": "int", "name": "str", "description": "str", "rate": "float", "applicable_from": "str", "applicable_to": "str", "is_default": "bool", "is_active": "bool", "tenant_id": "int", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0010528564453125}, "instrument_master.json": {"type": "list", "value": [], "file_size_mb": 2.86102294921875e-06}, "instruments.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "name": "str", "model": "str", "manufacturer": "str", "serial_number": "str", "installation_date": "str", "calibration_due": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0016613006591796875}, "inventory.json": {"type": "array", "count": 3, "sample_structure": {"id": "int", "name": "str", "sku": "str", "category": "str", "description": "str", "quantity": "int", "unit": "str", "reorder_level": "int", "cost_price": "float", "selling_price": "float", "supplier": "str", "location": "str", "expiry_date": "str", "tenant_id": "int", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0014629364013671875}, "invoices.json": {"type": "array", "count": 12, "sample_structure": {"id": "int", "invoice_number": "str", "routing_id": "int", "sample_id": "int", "from_tenant_id": "int", "to_tenant_id": "int", "created_by": "int", "created_at": "str", "updated_at": "str", "invoice_date": "str", "due_date": "str", "status": "str", "subtotal": "float", "tax_rate": "float", "tax_amount": "float", "total_amount": "float", "currency": "str", "notes": "str", "line_items": "array[dict]"}, "file_size_mb": 0.011745452880859375}, "main_department_master.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "major_department": "str", "code": "str", "department": "str", "order": "str", "short_name": "str", "queue": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0014514923095703125}, "method_master.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "code": "str", "method": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.000942230224609375}, "modules.json": {"type": "array", "count": 16, "sample_structure": {"id": "int", "name": "str", "code": "str", "description": "str", "route": "str", "icon": "str", "category": "str", "is_core": "bool", "is_active": "bool"}, "file_size_mb": 0.004027366638183594}, "notifications.json": {"type": "array", "count": 266, "sample_structure": {"id": "str", "type": "str", "title": "str", "message": "str", "recipient_id": "int", "sender_id": "int", "routing_id": "int", "priority": "str", "is_read": "bool", "created_at": "str", "read_at": "str", "data": "object"}, "file_size_mb": 0.****************}, "organism_master.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "code": "str", "description": "str", "no_growth": "bool", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0011196136474609375}, "organism_vs_antibiotic.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "organism": "str", "antibiotic_group": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0010662078857421875}, "parameter_master.json": {"type": "list", "value": [], "file_size_mb": 2.86102294921875e-06}, "patients.json": {"type": "array", "count": 89, "sample_structure": {"id": "int", "patient_id": "str", "first_name": "str", "last_name": "str", "gender": "str", "date_of_birth": "str", "phone": "str", "email": "str", "address": "str", "city": "str", "state": "str", "postal_code": "str", "blood_group": "str", "created_at": "str", "updated_at": "str", "tenant_id": "int", "created_by": "int"}, "file_size_mb": 0.04757499694824219}, "payment_methods.json": {"type": "array", "count": 6, "sample_structure": {"id": "int", "method_name": "str", "method_code": "str", "description": "str", "is_online": "bool", "processing_fee": "int", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int", "name": "str"}, "file_size_mb": 0.0017862319946289062}, "permissions.json": {"type": "array", "count": 17, "sample_structure": {"id": "int", "name": "str", "code": "str", "description": "str", "module": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.004981040954589844}, "price_scheme_master.json": {"type": "array", "count": 13, "sample_structure": {"id": "int", "dept_code": "str", "dept_name": "str", "scheme_code": "str", "scheme_name": "str", "test_type": "str", "test_code": "str", "test_name": "str", "default_price": "int", "scheme_price": "str", "price_percentage": "float", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.005833625793457031}, "print_order.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "item": "str", "order": "int", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0009164810180664062}, "profile_data.json": {"type": "array", "count": 3, "sample_structure": {"id": "int", "code": "str", "procedure_code": "str", "test_profile": "str", "test_price": "float", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.000774383544921875}, "profile_master.json": {"type": "array", "count": 6, "sample_structure": {"id": "int", "code": "str", "procedure_code": "str", "test_profile": "str", "test_price": "int", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.00156402587890625}, "profiles.json": {"type": "array", "count": 21, "sample_structure": {"code": "str", "procedure_code": "str", "test_profile": "str", "test_price": "str", "discount_price": "str", "emergency_price": "str", "home_visit_price": "str", "discount": "str", "category": "str", "test_count": "str", "is_active": "bool", "description": "str", "testItems": "array[dict]", "currentTest": "object", "gstRate": "int", "id": "str"}, "file_size_mb": 0.02145671844482422}, "quality_control_rules.json": {"type": "list", "value": [], "file_size_mb": 2.86102294921875e-06}, "reagent_master.json": {"type": "list", "value": [], "file_size_mb": 2.86102294921875e-06}, "reagents.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "name": "str", "lot_number": "str", "expiry_date": "str", "manufacturer": "str", "storage_temperature": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0015411376953125}, "reference_ranges.json": {"type": "list", "value": [], "file_size_mb": 2.86102294921875e-06}, "referralPricingMaster.json": {"type": "object", "structure": {"referralMaster": "object", "pricingSchemes": "object", "testPricingMatrix": "object", "discountRules": "object", "commissionRules": "object", "metadata": "object"}, "file_size_mb": 0.005316734313964844}, "result_master.json": {"type": "array", "count": 1, "sample_structure": {"id": "int", "result_name": "str", "parameter_name": "str", "test_name": "str", "unit": "str", "result_type": "str", "reference_range": "str", "normal_range": "str", "critical_low": "str", "critical_high": "str", "decimal_places": "str", "calculation_formula": "str", "validation_rules": "str", "display_order": "int", "is_calculated": "bool", "is_mandatory": "bool", "allow_manual_entry": "bool", "quality_control": "bool", "instrument_id": "str", "method_id": "str", "specimen_type": "str", "reporting_unit": "str", "conversion_factor": "int", "interpretation_rules": "str", "panic_values": "str", "delta_check_rules": "str", "age_specific_ranges": "str", "gender_specific_ranges": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0009164810180664062}, "result_master_enhanced.json": {"type": "array", "count": 790, "sample_structure": {"id": "int", "test_name": "str", "test_code": "str", "department": "str", "result_name": "str", "parameter_name": "str", "unit": "NoneType", "result_type": "str", "reference_range": "NoneType", "critical_low": "NoneType", "critical_high": "NoneType", "decimal_places": "int", "method": "NoneType", "specimen_type": "NoneType", "container": "NoneType", "instructions": "NoneType", "notes": "NoneType", "min_sample_qty": "NoneType", "excel_source": "bool", "source_sheet": "str", "is_active": "bool", "created_at": "str", "updated_at": "str"}, "file_size_mb": 0.6145267486572266}, "results.json": {"type": "array", "count": 212, "sample_structure": {"id": "int", "result_id": "str", "sample_id": "int", "test_id": "int", "value": "str", "unit": "str", "reference_range": "str", "status": "str", "result_date": "str", "created_at": "str", "updated_at": "str", "tenant_id": "int", "created_by": "int"}, "file_size_mb": 0.*****************}, "roles.json": {"type": "array", "count": 6, "sample_structure": {"id": "int", "name": "str", "code": "str", "description": "str", "permission_ids": "array[int]", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0018939971923828125}, "routing_files.json": {"type": "array", "count": 7, "sample_structure": {"id": "str", "routing_id": "int", "filename": "str", "content_type": "str", "file_size": "int", "uploaded_by": "int", "recipient_id": "int", "encrypted_content": "str", "is_encrypted": "bool", "created_at": "str", "description": "str"}, "file_size_mb": 3.***************}, "routing_messages.json": {"type": "array", "count": 23, "sample_structure": {"id": "str", "routing_id": "int", "sender_id": "int", "recipient_id": "int", "message_type": "str", "encrypted_content": "str", "is_encrypted": "bool", "created_at": "str", "is_read": "bool", "read_at": "str", "metadata": "object"}, "file_size_mb": 0.*****************}, "sample_routings.json": {"type": "array", "count": 25, "sample_structure": {"id": "int", "sample_id": "int", "from_tenant_id": "int", "to_tenant_id": "int", "reason": "str", "notes": "str", "priority": "str", "tracking_number": "str", "status": "str", "created_at": "str", "updated_at": "str", "created_by": "int", "dispatch_date": "str", "expected_delivery_date": "str", "actual_delivery_date": "str", "received_by": "int", "received_at": "str", "completed_at": "str", "completed_by": "int", "special_instructions": "str", "temperature_requirements": "str", "handling_requirements": "array[str]", "approved_by": "int", "approved_at": "str", "approval_notes": "str", "dispatched_by": "int", "courier_name": "str", "courier_contact": "str", "dispatch_notes": "str", "condition_on_arrival": "str", "receipt_notes": "str", "completion_notes": "str"}, "file_size_mb": 0.020712852478027344}, "sample_transfers.json": {"type": "array", "count": 18, "sample_structure": {"id": "int", "sample_id": "int", "from_tenant_id": "int", "to_tenant_id": "int", "reason": "str", "notes": "str", "status": "str", "created_at": "str", "updated_at": "str", "created_by": "int", "transferred_at": "str", "received_at": "str", "received_by": "int"}, "file_size_mb": 0.0073986053466796875}, "sample_types.json": {"type": "array", "count": 10, "sample_structure": {"id": "int", "type_name": "str", "type_code": "str", "description": "str"}, "file_size_mb": 0.0011110305786132812}, "samples.json": {"type": "array", "count": 100, "sample_structure": {"id": "int", "sample_id": "str", "patient_id": "int", "sample_type_id": "int", "sample_type": "str", "container_id": "int", "collection_date": "str", "collection_time": "str", "status": "str", "created_at": "str", "updated_at": "str", "tenant_id": "int", "collected_by": "int"}, "file_size_mb": 0.03815460205078125}, "schemes_master.json": {"type": "array", "count": 4, "sample_structure": {"id": "int", "scheme_code": "str", "scheme_name": "str", "description": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0009708404541015625}, "settings.json": {"type": "object", "structure": {"whatsapp": "object", "organization": "object", "security": "object", "lab": "object", "billing": "object"}, "file_size_mb": 0.0011882781982421875}, "special_package.json": {"type": "list", "value": [], "file_size_mb": 2.86102294921875e-06}, "specimen_master.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "code": "str", "specimen": "str", "container": "str", "disposable": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.00119781494140625}, "sub_test_master.json": {"type": "array", "count": 1, "sample_structure": {"id": "int", "sub_test_name": "str", "department_id": "str", "description": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.00024700164794921875}, "suppliers.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "name": "str", "contact_person": "str", "email": "str", "phone": "str", "address": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.001766204833984375}, "tenants.json": {"type": "array", "count": 12, "sample_structure": {"id": "int", "name": "str", "site_code": "str", "address": "str", "contact_phone": "str", "email": "str", "is_hub": "bool", "is_active": "bool", "use_site_code_prefix": "bool", "city": "str", "state": "str", "pincode": "str", "license_number": "str", "established_date": "str", "franchise_fee": "str", "monthly_fee": "str", "commission_rate": "str", "contact_person": "str", "contact_person_phone": "str", "notes": "str", "updated_at": "str"}, "file_size_mb": 0.0040302276611328125}, "test_categories.json": {"type": "array", "count": 8, "sample_structure": {"id": "int", "name": "str", "code": "str", "description": "str", "is_active": "bool", "created_at": "str", "updated_at": "str"}, "file_size_mb": 0.00157928466796875}, "test_master.json": {"type": "array", "count": 299, "sample_structure": {"id": "int", "testName": "str", "hmsCode": "str", "department": "str", "instructions": "str", "reference_range": "str", "result_unit": "str", "decimals": "int", "test_price": "float", "critical_low": "NoneType", "critical_high": "NoneType", "alertMessage": "str", "alertPeriod": "str", "alertSMS": "bool", "applicableTo": "str", "container": "str", "created_at": "str", "created_by": "str", "cutoffTime": "str", "displayName": "str", "emergencyProcessPeriod": "str", "emergencyProcessTime": "int", "emrClassification": "str", "expiryPeriod": "str", "expiryTime": "int", "internationalCode": "str", "interpretation": "str", "is_active": "bool", "method": "str", "minProcessPeriod": "str", "minProcessTime": "int", "minSampleQty": "str", "options": "object", "primarySpecimen": "str", "reportName": "str", "reportingDays": "array[str]", "serviceTime": "str", "shortName": "str", "specialReport": "str", "specimen": "array[str]", "subTests": "array[empty]", "suffixDesc": "str", "testDoneOn": "array[str]", "testSuffix": "str", "test_profile": "str", "unacceptableConditions": "array[str]", "updated_at": "str"}, "file_size_mb": 0.4962787628173828}, "test_master_enhanced.json": {"type": "array", "count": 790, "sample_structure": {"id": "int", "department": "str", "testName": "str", "hmsCode": "str", "test_code": "str", "short_name": "NoneType", "method": "NoneType", "method_code": "NoneType", "specimen": "array[empty]", "specimen_code": "NoneType", "container": "NoneType", "container_code": "NoneType", "reference_range": "NoneType", "result_unit": "NoneType", "decimals": "int", "critical_low": "NoneType", "critical_high": "NoneType", "test_price": "int", "result_type": "str", "instructions": "NoneType", "notes": "NoneType", "min_sample_qty": "NoneType", "serviceTime": "str", "reporting_days": "int", "test_done_on": "str", "applicable_to": "str", "excel_source": "bool", "source_sheet": "str", "is_active": "bool", "created_at": "str", "updated_at": "str"}, "file_size_mb": 0.7568769454956055}, "test_methods.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "name": "str", "description": "str", "principle": "str", "procedure": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0024671554565429688}, "test_panels.json": {"type": "list", "value": [], "file_size_mb": 1.9073486328125e-06}, "test_parameters.json": {"type": "array", "count": 6, "sample_structure": {"id": "int", "parameter_name": "str", "parameter_code": "str", "unit": "str", "reference_range": "str", "method": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int", "name": "str", "category_id": "str"}, "file_size_mb": 0.0019025802612304688}, "test_sub_process.json": {"type": "list", "value": [], "file_size_mb": 2.86102294921875e-06}, "tests.json": {"type": "array", "count": 10, "sample_structure": {"id": "int", "test_name": "str", "sample_type_id": "int", "turnaround_time": "str", "price": "int"}, "file_size_mb": 0.0013418197631835938}, "unit_of_measurement.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "code": "str", "description": "str", "technical": "str", "inventory": "str", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.001117706298828125}, "units.json": {"type": "array", "count": 8, "sample_structure": {"id": "int", "name": "str", "symbol": "str", "type": "str", "conversion_factor": "float", "is_active": "bool", "created_at": "str", "updated_at": "str", "created_by": "int"}, "file_size_mb": 0.0019865036010742188}, "users.json": {"type": "array", "count": 16, "sample_structure": {"id": "int", "username": "str", "password": "str", "email": "str", "first_name": "str", "last_name": "str", "role": "str", "tenant_id": "int", "is_active": "bool"}, "file_size_mb": 0.0041980743408203125}, "whatsapp_config.json": {"type": "array", "count": 1, "sample_structure": {"id": "int", "tenant_id": "int", "api_key": "str", "api_secret": "str", "phone_number_id": "str", "business_account_id": "str", "is_enabled": "bool", "default_report_template": "str", "default_invoice_template": "str", "created_at": "str", "updated_at": "str"}, "file_size_mb": 0.0005998611450195312}, "whatsapp_messages.json": {"type": "array", "count": 5, "sample_structure": {"id": "int", "tenant_id": "int", "user_id": "int", "recipient_number": "str", "message_content": "str", "message_type": "str", "order_id": "int", "billing_id": "NoneType", "status": "str", "created_at": "str", "sent_at": "str", "delivered_at": "NoneType", "message_id": "str", "error_message": "NoneType"}, "file_size_mb": 0.*****************}, "workflow_instances.json": {"type": "array", "count": 25, "sample_structure": {"id": "str", "routing_id": "int", "workflow_type": "str", "current_stage": "str", "created_at": "str", "created_by": "int", "updated_at": "str", "stage_history": "array[dict]"}, "file_size_mb": 0.024209022521972656}}, "relationships": {"audit_trail.json": [{"field": "user_id", "type": "int", "potential_reference": "user"}, {"field": "tenant_id", "type": "int", "potential_reference": "tenant"}], "billing.json": [{"field": "patient_id", "type": "int", "potential_reference": "patient"}, {"field": "tenant_id", "type": "int", "potential_reference": "tenant"}], "billing_reports.json": [{"field": "billing_id", "type": "int", "potential_reference": "billing"}, {"field": "patient_id", "type": "int", "potential_reference": "patient"}, {"field": "tenant_id", "type": "int", "potential_reference": "tenant"}], "billings.json": [{"field": "patient_id", "type": "int", "potential_reference": "patient"}, {"field": "tenant_id", "type": "int", "potential_reference": "tenant"}], "containers.json": [{"field": "sample_type_id", "type": "int", "potential_reference": "sample_type"}], "error_log.json": [{"field": "user_id", "type": "int", "potential_reference": "user"}, {"field": "tenant_id", "type": "int", "potential_reference": "tenant"}], "franchise_permissions.json": [{"field": "franchise_id", "type": "int", "potential_reference": "franchise"}], "gst_config.json": [{"field": "tenant_id", "type": "int", "potential_reference": "tenant"}], "inventory.json": [{"field": "tenant_id", "type": "int", "potential_reference": "tenant"}], "invoices.json": [{"field": "routing_id", "type": "int", "potential_reference": "routing"}, {"field": "sample_id", "type": "int", "potential_reference": "sample"}, {"field": "from_tenant_id", "type": "int", "potential_reference": "from_tenant"}, {"field": "to_tenant_id", "type": "int", "potential_reference": "to_tenant"}], "notifications.json": [{"field": "recipient_id", "type": "int", "potential_reference": "recipient"}, {"field": "sender_id", "type": "int", "potential_reference": "sender"}, {"field": "routing_id", "type": "int", "potential_reference": "routing"}], "patients.json": [{"field": "patient_id", "type": "str", "potential_reference": "patient"}, {"field": "tenant_id", "type": "int", "potential_reference": "tenant"}], "result_master.json": [{"field": "instrument_id", "type": "str", "potential_reference": "instrument"}, {"field": "method_id", "type": "str", "potential_reference": "method"}], "results.json": [{"field": "result_id", "type": "str", "potential_reference": "result"}, {"field": "sample_id", "type": "int", "potential_reference": "sample"}, {"field": "test_id", "type": "int", "potential_reference": "test"}, {"field": "tenant_id", "type": "int", "potential_reference": "tenant"}], "routing_files.json": [{"field": "routing_id", "type": "int", "potential_reference": "routing"}, {"field": "recipient_id", "type": "int", "potential_reference": "recipient"}], "routing_messages.json": [{"field": "routing_id", "type": "int", "potential_reference": "routing"}, {"field": "sender_id", "type": "int", "potential_reference": "sender"}, {"field": "recipient_id", "type": "int", "potential_reference": "recipient"}], "sample_routings.json": [{"field": "sample_id", "type": "int", "potential_reference": "sample"}, {"field": "from_tenant_id", "type": "int", "potential_reference": "from_tenant"}, {"field": "to_tenant_id", "type": "int", "potential_reference": "to_tenant"}], "sample_transfers.json": [{"field": "sample_id", "type": "int", "potential_reference": "sample"}, {"field": "from_tenant_id", "type": "int", "potential_reference": "from_tenant"}, {"field": "to_tenant_id", "type": "int", "potential_reference": "to_tenant"}], "samples.json": [{"field": "sample_id", "type": "str", "potential_reference": "sample"}, {"field": "patient_id", "type": "int", "potential_reference": "patient"}, {"field": "sample_type_id", "type": "int", "potential_reference": "sample_type"}, {"field": "container_id", "type": "int", "potential_reference": "container"}, {"field": "tenant_id", "type": "int", "potential_reference": "tenant"}], "sub_test_master.json": [{"field": "department_id", "type": "str", "potential_reference": "department"}], "test_parameters.json": [{"field": "category_id", "type": "str", "potential_reference": "category"}], "tests.json": [{"field": "sample_type_id", "type": "int", "potential_reference": "sample_type"}], "users.json": [{"field": "tenant_id", "type": "int", "potential_reference": "tenant"}], "whatsapp_config.json": [{"field": "tenant_id", "type": "int", "potential_reference": "tenant"}, {"field": "phone_number_id", "type": "str", "potential_reference": "phone_number"}, {"field": "business_account_id", "type": "str", "potential_reference": "business_account"}], "whatsapp_messages.json": [{"field": "tenant_id", "type": "int", "potential_reference": "tenant"}, {"field": "user_id", "type": "int", "potential_reference": "user"}, {"field": "order_id", "type": "int", "potential_reference": "order"}, {"field": "message_id", "type": "str", "potential_reference": "message"}], "workflow_instances.json": [{"field": "routing_id", "type": "int", "potential_reference": "routing"}]}}