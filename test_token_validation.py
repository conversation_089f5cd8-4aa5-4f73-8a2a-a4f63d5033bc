#!/usr/bin/env python3
"""
Test script to verify token validation and user data
"""
import requests
import json

def test_token_validation():
    """Test token validation and user data retrieval"""
    print("Testing Token Validation...")
    
    # First, login to get a token
    login_data = {
        "username": "soorya",
        "password": "12345678"
    }
    
    try:
        # Login
        response = requests.post(
            "http://localhost:5002/api/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"✗ Login failed: {response.text}")
            return
            
        data = response.json()
        token = data.get('token')
        user = data.get('user')
        
        print("✓ Login successful")
        print(f"  User: {user}")
        
        # Test token validation
        headers = {"Authorization": f"Bearer {token}"}
        
        validate_response = requests.post(
            "http://localhost:5002/api/auth/validate",
            headers=headers
        )
        
        print(f"\nToken Validation Status: {validate_response.status_code}")
        
        if validate_response.status_code == 200:
            validate_data = validate_response.json()
            print("✓ Token validation successful")
            print(f"  Valid: {validate_data.get('valid')}")
            print(f"  User ID: {validate_data.get('user_id')}")
            print(f"  Role: {validate_data.get('role')}")
        else:
            print(f"✗ Token validation failed: {validate_response.text}")
            
        # Test module access check
        module_response = requests.get(
            "http://localhost:5002/api/access-management/check-module-access/USER_MANAGEMENT",
            headers=headers
        )
        
        print(f"\nModule Access Check Status: {module_response.status_code}")
        
        if module_response.status_code == 200:
            module_data = module_response.json()
            print("✓ Module access check successful")
            print(f"  Has Access: {module_data.get('has_access')}")
            print(f"  Message: {module_data.get('message')}")
        else:
            print(f"✗ Module access check failed: {module_response.text}")
            
    except Exception as e:
        print(f"✗ Error: {str(e)}")

if __name__ == "__main__":
    test_token_validation()
