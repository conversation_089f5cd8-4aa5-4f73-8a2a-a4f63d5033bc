// utils/printBilling.js
// adjust path
import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import { Card, Button, Badge, Row, Col,Modal, Form, InputGroup, } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faFileInvoiceDollar, faArrowLeft, faPrint, faMoneyBillWave,
  faUser, faCalendarAlt, faRupeeSign, faPlus, faCheckCircle, faShare,faExclamationTriangle,faInfoCircle
} from '@fortawesome/free-solid-svg-icons';
import { adminAPI, billingAPI } from '../../services/api';
import { InfoModal, SuccessModal, ErrorModal } from '../../components/common';
import WhatsAppSend from '../../components/common/WhatsAppSend';
import ResponsiveInvoiceItemsTable from '../../components/billing/ResponsiveInvoiceItemsTable';
import '../../styles/BillingView.css';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import bwipjs from 'bwip-js';
import logo from '../../assets/logoavini.png'; // adjust the path as needed
import { useAuth } from '../../context/AuthContext';
import { useTenant } from '../../context/TenantContext';
import { addTestToBilling } from '../../services/billingAPI';
import dynamicPricingService from '../../services/dynamicPricingService';
import referrerMasterData from '../../data/referrerMasterData.json';

export const printBillingPDF = async (billing) => {

    const generateBarcodeBytes = async (text) => {
      const canvas = document.createElement('canvas');
      bwipjs.toCanvas(canvas, {
        bcid: 'code128',
        text,
        scale: 1,
        height: 7,
        includetext: false,
      });
      const dataUrl = canvas.toDataURL('image/png');
      const response = await fetch(dataUrl);
      return await response.arrayBuffer();
    };

    const generateInvoicePdf = async (billing, logoImageBytes) => {
      const pdfDoc = await PDFDocument.create();
      const page = pdfDoc.addPage([595, 842]); // A4
      const helvetica = await pdfDoc.embedFont(StandardFonts.Helvetica);
      const bold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);
      const { width } = page.getSize();
    
      const drawText = (text, x, y, options = {}) => {
        page.drawText(text.toString(), {
          x,
          y,
          font: options.bold ? bold : helvetica,
          size: options.size || 10,
          color: rgb(0, 0, 0),
        });
      };
    
      let y = 770;
    
      // ▓▓ Logo (top-left)
      if (logoImageBytes) {
        const logoImage = await pdfDoc.embedPng(logoImageBytes);
        const scaled = logoImage.scale(0.2);
        page.drawImage(logoImage, {
          x: 30,
          y: y,
          width: scaled.width,
          height: scaled.height,
        });
      }
    
      // ▓▓ Center Header
      drawText('AVINI LABS', 250, y, { bold: true, size: 14 });
      y -= 15;
      drawText('No. 69, Mahadhana Street. Mayiladuthurai.', 170, y, { size: 10 });
      y -= 15;
      drawText(`${new Date().toLocaleString()}`, 230, y, { size: 10 }); // Use new Date().toLocaleString() for dynamic
    
      // ▓▓ Barcode in top-right (ABOVE SID)
      if (billing.sid_number) {
        try {
          const barcodeBytes = await generateBarcodeBytes(billing.sid_number.toString());
          const barcodeImage = await pdfDoc.embedPng(barcodeBytes);
          page.drawImage(barcodeImage, {
            x: 400,    // Top-right
            y: y + 10,
            width: 130,
            height: 40,
          });
        } catch (e) {
          console.error('Barcode error:', e);
        }
      }
    
      y -= 25;
    
      // ▓▓ Line under header
      page.drawLine({ start: { x: 50, y }, end: { x: 545, y }, thickness: 1, color: rgb(0, 0, 0) });
    
      y -= 30;
    
      // ▓▓ Patient Info Block
     // ▓▓ Structured Patient Info (3 rows)
    drawText(`PID No. : ${billing.patient?.patient_id ?? 'N/A'}`, 50, y, { bold: true });
    drawText(`SID No. ${billing.sid_number ?? 'N/A'}`, 350, y, { bold: true });
    y -= 15;
    const patientName = (
      billing.patient?.full_name ??
      `${billing.patient?.first_name ?? ''} ${billing.patient?.last_name ?? ''}`
    ).toUpperCase();
    
    const age = billing.patient?.age ?? 'N/A';
    const gender = billing.patient?.gender?.[0]?.toUpperCase() ?? '-';
    drawText(`patient :MS. ${patientName} (${age} Y / ${gender}).`, 50, y, { bold: true });
    
    drawText(`SID Date ${new Date(billing.invoice_date).toLocaleString()}`, 350, y, { bold: true });
    y -= 15;
    
    drawText(`Referrer :${billing.doctor_name ?? 'N/A'}`, 50, y, { bold: true });
    drawText(`Print Date:${new Date().toLocaleString()}`, 350, y, { bold: true });
    
    
      y -= 25;
    
      // ▓▓ Table Header
      page.drawLine({ start: { x: 50, y }, end: { x: 545, y }, thickness: 1, color: rgb(0, 0, 0) });
      y -= 15;
      drawText('S.No', 50, y, { bold: true });
      drawText('Description', 120, y, { bold: true });
      drawText('Amount', 450, y, { bold: true });
      y -= 10;
      page.drawLine({ start: { x: 50, y }, end: { x: 545, y }, thickness: 1, color: rgb(0, 0, 0) });
      y -= 15;
    
      // ▓▓ Items
      billing.items?.forEach((item, i) => {
        drawText(`${i + 1}`, 50, y);
        drawText(item.name || item.test_name || 'N/A', 120, y);
        drawText(parseFloat(item.amount ?? 0).toFixed(2), 450, y);
        y -= 15;
      });
    
      y -= 5;
      page.drawLine({ start: { x: 50, y }, end: { x: 545, y }, thickness: 1, color: rgb(0, 0, 0) });
    
      // ▓▓ Totals
      const total = parseFloat(billing.total_amount ?? 0);
      const discount = parseFloat(billing.discount ?? 0);
      const paid = parseFloat(billing.paid_amount ?? 0);
      const due = total - paid;
    
      y -= 20;
      drawText('Bill Amount', 400, y, { bold: true });
      drawText(total.toFixed(2), 500, y);
    
      y -= 15;
      drawText('Discount', 400, y);
      drawText(discount.toFixed(2), 500, y);
    
      y -= 15;
      drawText('Total Amount', 400, y, { bold: true });
      drawText((total - discount).toFixed(2), 500, y);
    
      y -= 10;
      page.drawLine({ start: { x: 300, y }, end: { x: 545, y }, thickness: 1, color: rgb(0, 0, 0) });
    
      y -= 20;
      drawText('Due Amount', 400, y, { bold: true });
      drawText(due.toFixed(2), 500, y);
    
      y -= 10;
      page.drawLine({ start: { x: 300, y }, end: { x: 545, y }, thickness: 1, color: rgb(0, 0, 0) });
    
      // ▓▓ Signature Block
      y -= 40;
      drawText('For AVINI LABS', 400, y);
      y -= 15;
      drawText('Authorised Signatory', 400, y);
    
      // ▓▓ Footer Note
      y -= 40;
      drawText('Note:', 50, y, { bold: true });
      y -= 15;
      drawText('This is an Electronically generated Receipt & Does Not Require Signature.', 50, y, { size: 9 });
      y -= 15;
      drawText('Report will be sent via email or online only after full payment.', 50, y, { size: 9 });
      y -= 15;
      drawText(`Bill User: ${billing.bill_user ?? 'N/A'}`, 50, y, { size: 9 });
    
      const pdfBytes = await pdfDoc.save();
      return pdfBytes;
    };
    const fetchLogoBytes = async () => {
      const response = await fetch(logo);
      const logoBytes = await response.arrayBuffer();
      return logoBytes;
    };
  const logoBytes = await fetchLogoBytes();
  const pdfBytes = await generateInvoicePdf(billing, logoBytes);
  const blob = new Blob([pdfBytes], { type: "application/pdf" });
  const url = URL.createObjectURL(blob);
  window.open(url); // opens in new tab, user can click Print
};
