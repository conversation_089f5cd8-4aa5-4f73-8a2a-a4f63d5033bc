#!/usr/bin/env python3
"""
Database Connection Manager for AVINI Labs
Handles SQLite database connections, transactions, and query operations
"""

import sqlite3
import os
import threading
import json
from contextlib import contextmanager
from typing import List, Dict, Any, Optional, Union
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """Centralized database connection and query manager"""
    
    def __init__(self, db_path: str = None):
        if db_path is None:
            # Default to the database in the data directory
            base_dir = os.path.dirname(os.path.abspath(__file__))
            self.db_path = os.path.join(base_dir, 'data', 'avini_labs.db')
        else:
            self.db_path = db_path
        
        # Thread-local storage for connections
        self._local = threading.local()
        
        # Ensure database exists
        if not os.path.exists(self.db_path):
            logger.warning(f"Database not found at {self.db_path}")
            logger.info("Please run database_init.py to create the database")
    
    def get_connection(self) -> sqlite3.Connection:
        """Get a database connection for the current thread"""
        if not hasattr(self._local, 'connection') or self._local.connection is None:
            self._local.connection = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0
            )
            # Enable foreign key constraints
            self._local.connection.execute("PRAGMA foreign_keys = ON")
            # Set row factory for dict-like access
            self._local.connection.row_factory = sqlite3.Row
        
        return self._local.connection
    
    def close_connection(self):
        """Close the current thread's database connection"""
        if hasattr(self._local, 'connection') and self._local.connection:
            self._local.connection.close()
            self._local.connection = None
    
    @contextmanager
    def transaction(self):
        """Context manager for database transactions"""
        conn = self.get_connection()
        try:
            yield conn
            conn.commit()
        except Exception as e:
            conn.rollback()
            logger.error(f"Transaction failed: {e}")
            raise
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """Execute a SELECT query and return results as list of dictionaries"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            # Convert rows to dictionaries
            columns = [description[0] for description in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            
            return results
        
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise
    
    def execute_insert(self, query: str, params: tuple = None) -> int:
        """Execute an INSERT query and return the last row ID"""
        with self.transaction() as conn:
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            return cursor.lastrowid
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """Execute an UPDATE query and return the number of affected rows"""
        with self.transaction() as conn:
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            return cursor.rowcount
    
    def execute_delete(self, query: str, params: tuple = None) -> int:
        """Execute a DELETE query and return the number of affected rows"""
        with self.transaction() as conn:
            cursor = conn.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            return cursor.rowcount
    
    def get_by_id(self, table: str, record_id: int) -> Optional[Dict]:
        """Get a single record by ID"""
        query = f"SELECT * FROM {table} WHERE id = ?"
        results = self.execute_query(query, (record_id,))
        return results[0] if results else None
    
    def get_all(self, table: str, where_clause: str = None, params: tuple = None, 
                order_by: str = None, limit: int = None) -> List[Dict]:
        """Get all records from a table with optional filtering"""
        query = f"SELECT * FROM {table}"
        
        if where_clause:
            query += f" WHERE {where_clause}"
        
        if order_by:
            query += f" ORDER BY {order_by}"
        
        if limit:
            query += f" LIMIT {limit}"
        
        return self.execute_query(query, params)
    
    def insert_record(self, table: str, data: Dict) -> int:
        """Insert a new record and return the ID"""
        # Filter out None values and prepare data
        filtered_data = {k: v for k, v in data.items() if v is not None}
        
        columns = list(filtered_data.keys())
        placeholders = ', '.join(['?' for _ in columns])
        values = list(filtered_data.values())
        
        query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({placeholders})"
        
        return self.execute_insert(query, tuple(values))
    
    def update_record(self, table: str, record_id: int, data: Dict) -> int:
        """Update a record by ID"""
        # Filter out None values and prepare data
        filtered_data = {k: v for k, v in data.items() if v is not None}
        
        # Add updated_at timestamp
        filtered_data['updated_at'] = datetime.now().isoformat()
        
        set_clause = ', '.join([f"{k} = ?" for k in filtered_data.keys()])
        values = list(filtered_data.values()) + [record_id]
        
        query = f"UPDATE {table} SET {set_clause} WHERE id = ?"
        
        return self.execute_update(query, tuple(values))
    
    def delete_record(self, table: str, record_id: int) -> int:
        """Delete a record by ID"""
        query = f"DELETE FROM {table} WHERE id = ?"
        return self.execute_delete(query, (record_id,))
    
    def search_records(self, table: str, search_fields: List[str], 
                      search_term: str, limit: int = 100) -> List[Dict]:
        """Search records across multiple fields"""
        where_conditions = []
        params = []
        
        for field in search_fields:
            where_conditions.append(f"{field} LIKE ?")
            params.append(f"%{search_term}%")
        
        where_clause = " OR ".join(where_conditions)
        
        return self.get_all(table, where_clause, tuple(params), limit=limit)
    
    def get_paginated(self, table: str, page: int = 1, per_page: int = 20, 
                     where_clause: str = None, params: tuple = None,
                     order_by: str = None) -> Dict:
        """Get paginated results"""
        offset = (page - 1) * per_page
        
        # Get total count
        count_query = f"SELECT COUNT(*) as total FROM {table}"
        if where_clause:
            count_query += f" WHERE {where_clause}"
        
        count_result = self.execute_query(count_query, params)
        total = count_result[0]['total'] if count_result else 0
        
        # Get paginated data
        data_query = f"SELECT * FROM {table}"
        if where_clause:
            data_query += f" WHERE {where_clause}"
        if order_by:
            data_query += f" ORDER BY {order_by}"
        data_query += f" LIMIT {per_page} OFFSET {offset}"
        
        data = self.execute_query(data_query, params)
        
        return {
            'data': data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        }
    
    def backup_database(self, backup_path: str = None) -> str:
        """Create a backup of the database"""
        if backup_path is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = f"{self.db_path}.backup_{timestamp}"
        
        # Use SQLite backup API
        source = self.get_connection()
        backup = sqlite3.connect(backup_path)
        
        try:
            source.backup(backup)
            logger.info(f"Database backed up to: {backup_path}")
            return backup_path
        finally:
            backup.close()
    
    def get_table_info(self, table: str) -> List[Dict]:
        """Get table schema information"""
        query = f"PRAGMA table_info({table})"
        return self.execute_query(query)
    
    def get_all_tables(self) -> List[str]:
        """Get list of all tables in the database"""
        query = "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
        results = self.execute_query(query)
        return [row['name'] for row in results]
    
    def execute_raw_sql(self, sql: str, params: tuple = None) -> List[Dict]:
        """Execute raw SQL query (use with caution)"""
        return self.execute_query(sql, params)

    def execute_ddl(self, sql: str) -> bool:
        """Execute DDL statements (CREATE, ALTER, DROP) safely"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute(sql)
            conn.commit()
            logger.info(f"DDL executed successfully: {sql[:100]}...")
            return True

        except Exception as e:
            conn.rollback()
            logger.error(f"DDL execution failed: {e}")
            logger.error(f"SQL: {sql}")
            raise

# Global database manager instance
db_manager = DatabaseManager()

# Convenience functions for backward compatibility
def read_data(filename: str) -> List[Dict]:
    """
    Backward compatibility function for JSON file reading
    Maps JSON filenames to database tables
    """
    # Map JSON filenames to database tables
    table_mapping = {
        'tenants.json': 'tenants',
        'users.json': 'users',
        'patients.json': 'patients',
        'samples.json': 'samples',
        'billings.json': 'billings',
        'results.json': 'results',
        'departments.json': 'departments',
        'test_master.json': 'test_master',
        'test_categories.json': 'test_categories',
        'sample_types.json': 'sample_types',
        'containers.json': 'containers',
        'payment_methods.json': 'payment_methods',
        'roles.json': 'roles',
        'permissions.json': 'permissions'
    }
    
    table_name = table_mapping.get(filename)
    if table_name:
        return db_manager.get_all(table_name)
    else:
        logger.warning(f"No table mapping found for {filename}")
        return []

def write_data(filename: str, data: List[Dict]):
    """
    Backward compatibility function for JSON file writing
    This function is deprecated - use database operations instead
    """
    logger.warning(f"write_data() called for {filename} - this function is deprecated")
    logger.warning("Please use database operations instead of JSON file operations")

# Export the database manager instance
__all__ = ['DatabaseManager', 'db_manager', 'read_data', 'write_data']
