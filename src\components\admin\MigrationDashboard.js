import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  ProgressBar, 
  Alert, 
  Button, 
  Badge,
  Table,
  Modal,
  Spinner
} from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faDatabase, 
  faCheckCircle, 
  faTimesCircle, 
  faExclamationTriangle,
  faPlay,
  faStop,
  faRefresh,
  faDownload,
  faUpload,
  faSpinner,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';
import { adminAPI } from '../../services/adminAPI';

const MigrationDashboard = () => {
  const [migrationStatus, setMigrationStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [migrating, setMigrating] = useState(false);
  const [showLogModal, setShowLogModal] = useState(false);
  const [migrationLogs, setMigrationLogs] = useState([]);
  const [error, setError] = useState(null);

  // Fetch migration status
  const fetchMigrationStatus = async () => {
    try {
      setLoading(true);
      const response = await adminAPI.get('/admin/migration/status');
      setMigrationStatus(response.data);
      setError(null);
    } catch (err) {
      console.error('Error fetching migration status:', err);
      setError('Failed to fetch migration status');
    } finally {
      setLoading(false);
    }
  };

  // Start migration
  const startMigration = async () => {
    try {
      setMigrating(true);
      setError(null);
      
      const response = await adminAPI.post('/admin/migration/start');
      
      // Poll for status updates
      const pollInterval = setInterval(async () => {
        try {
          const statusResponse = await adminAPI.get('/admin/migration/status');
          setMigrationStatus(statusResponse.data);
          
          if (statusResponse.data.status === 'completed' || statusResponse.data.status === 'failed') {
            clearInterval(pollInterval);
            setMigrating(false);
          }
        } catch (err) {
          console.error('Error polling migration status:', err);
          clearInterval(pollInterval);
          setMigrating(false);
        }
      }, 2000);
      
    } catch (err) {
      console.error('Error starting migration:', err);
      setError(err.response?.data?.message || 'Failed to start migration');
      setMigrating(false);
    }
  };

  // Fetch migration logs
  const fetchMigrationLogs = async () => {
    try {
      const response = await adminAPI.get('/admin/migration/logs');
      setMigrationLogs(response.data.logs || []);
      setShowLogModal(true);
    } catch (err) {
      console.error('Error fetching migration logs:', err);
      setError('Failed to fetch migration logs');
    }
  };

  useEffect(() => {
    fetchMigrationStatus();
  }, []);

  const getStatusVariant = (status) => {
    switch (status) {
      case 'completed': return 'success';
      case 'failed': return 'danger';
      case 'running': return 'primary';
      case 'pending': return 'warning';
      default: return 'secondary';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return faCheckCircle;
      case 'failed': return faTimesCircle;
      case 'running': return faSpinner;
      case 'pending': return faExclamationTriangle;
      default: return faInfoCircle;
    }
  };

  if (loading) {
    return (
      <div className="text-center p-4">
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
        <p className="mt-2">Loading migration status...</p>
      </div>
    );
  }

  return (
    <div className="migration-dashboard">
      <Row className="mb-4">
        <Col>
          <h4>
            <FontAwesomeIcon icon={faDatabase} className="me-2" />
            Data Migration Dashboard
          </h4>
          <p className="text-muted">
            Monitor and manage the migration of data from JSON files to SQLite database
          </p>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          <FontAwesomeIcon icon={faTimesCircle} className="me-2" />
          {error}
        </Alert>
      )}

      {/* Migration Overview */}
      <Row className="mb-4">
        <Col md={8}>
          <Card>
            <Card.Header>
              <h5 className="mb-0">Migration Overview</h5>
            </Card.Header>
            <Card.Body>
              {migrationStatus ? (
                <>
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <span>Overall Progress</span>
                    <Badge variant={getStatusVariant(migrationStatus.status)}>
                      <FontAwesomeIcon 
                        icon={getStatusIcon(migrationStatus.status)} 
                        className={migrationStatus.status === 'running' ? 'fa-spin me-1' : 'me-1'} 
                      />
                      {migrationStatus.status?.toUpperCase()}
                    </Badge>
                  </div>
                  
                  <ProgressBar 
                    now={migrationStatus.progress || 0} 
                    label={`${migrationStatus.progress || 0}%`}
                    variant={getStatusVariant(migrationStatus.status)}
                    className="mb-3"
                  />

                  <Row>
                    <Col sm={6}>
                      <small className="text-muted">Started:</small>
                      <div>{migrationStatus.started_at ? new Date(migrationStatus.started_at).toLocaleString() : 'Not started'}</div>
                    </Col>
                    <Col sm={6}>
                      <small className="text-muted">Completed:</small>
                      <div>{migrationStatus.completed_at ? new Date(migrationStatus.completed_at).toLocaleString() : 'In progress'}</div>
                    </Col>
                  </Row>

                  {migrationStatus.error && (
                    <Alert variant="danger" className="mt-3 mb-0">
                      <strong>Error:</strong> {migrationStatus.error}
                    </Alert>
                  )}
                </>
              ) : (
                <Alert variant="info">
                  <FontAwesomeIcon icon={faInfoCircle} className="me-2" />
                  No migration data available. Click "Start Migration" to begin.
                </Alert>
              )}
            </Card.Body>
          </Card>
        </Col>

        <Col md={4}>
          <Card>
            <Card.Header>
              <h5 className="mb-0">Actions</h5>
            </Card.Header>
            <Card.Body>
              <div className="d-grid gap-2">
                <Button
                  variant="primary"
                  onClick={startMigration}
                  disabled={migrating || migrationStatus?.status === 'running'}
                >
                  {migrating ? (
                    <>
                      <FontAwesomeIcon icon={faSpinner} spin className="me-2" />
                      Migrating...
                    </>
                  ) : (
                    <>
                      <FontAwesomeIcon icon={faPlay} className="me-2" />
                      Start Migration
                    </>
                  )}
                </Button>

                <Button
                  variant="outline-secondary"
                  onClick={fetchMigrationStatus}
                  disabled={migrating}
                >
                  <FontAwesomeIcon icon={faRefresh} className="me-2" />
                  Refresh Status
                </Button>

                <Button
                  variant="outline-info"
                  onClick={fetchMigrationLogs}
                  disabled={migrating}
                >
                  <FontAwesomeIcon icon={faDownload} className="me-2" />
                  View Logs
                </Button>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Table Status */}
      {migrationStatus?.tables && (
        <Row>
          <Col>
            <Card>
              <Card.Header>
                <h5 className="mb-0">Table Migration Status</h5>
              </Card.Header>
              <Card.Body>
                <Table responsive striped>
                  <thead>
                    <tr>
                      <th>Table</th>
                      <th>Status</th>
                      <th>Records</th>
                      <th>Progress</th>
                      <th>Duration</th>
                      <th>Error</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Object.entries(migrationStatus.tables).map(([tableName, tableStatus]) => (
                      <tr key={tableName}>
                        <td>
                          <strong>{tableName}</strong>
                        </td>
                        <td>
                          <Badge variant={getStatusVariant(tableStatus.status)}>
                            <FontAwesomeIcon 
                              icon={getStatusIcon(tableStatus.status)} 
                              className={tableStatus.status === 'running' ? 'fa-spin me-1' : 'me-1'} 
                            />
                            {tableStatus.status}
                          </Badge>
                        </td>
                        <td>
                          {tableStatus.processed || 0} / {tableStatus.total || 0}
                        </td>
                        <td>
                          <ProgressBar 
                            now={tableStatus.progress || 0} 
                            size="sm"
                            variant={getStatusVariant(tableStatus.status)}
                          />
                        </td>
                        <td>
                          {tableStatus.duration ? `${tableStatus.duration}ms` : '-'}
                        </td>
                        <td>
                          {tableStatus.error ? (
                            <span className="text-danger small">{tableStatus.error}</span>
                          ) : (
                            '-'
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {/* Migration Logs Modal */}
      <Modal show={showLogModal} onHide={() => setShowLogModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Migration Logs</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
            {migrationLogs.length > 0 ? (
              <pre className="bg-light p-3 rounded">
                {migrationLogs.join('\n')}
              </pre>
            ) : (
              <Alert variant="info">
                <FontAwesomeIcon icon={faInfoCircle} className="me-2" />
                No migration logs available.
              </Alert>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowLogModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default MigrationDashboard;
