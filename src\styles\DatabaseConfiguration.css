/* Database Configuration Component Styles */

.database-configuration {
  color: var(--bs-body-color);
  background-color: var(--bs-body-bg);
}

/* Table List Styling */
.database-configuration .list-group-item {
  background-color: var(--bs-body-bg);
  border-color: var(--bs-border-color);
  color: var(--bs-body-color);
}

.database-configuration .list-group-item:hover {
  background-color: var(--bs-secondary-bg);
}

.database-configuration .list-group-item.active {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  color: var(--bs-white);
}

/* Card Styling */
.database-configuration .card {
  background-color: var(--bs-body-bg);
  border-color: var(--bs-border-color);
  color: var(--bs-body-color);
}

.database-configuration .card-header {
  background-color: var(--bs-secondary-bg);
  border-bottom-color: var(--bs-border-color);
  color: var(--bs-body-color);
}

.database-configuration .card-body {
  background-color: var(--bs-body-bg);
  color: var(--bs-body-color);
}

/* Table Styling */
.database-configuration .table {
  color: var(--bs-body-color);
  --bs-table-bg: var(--bs-body-bg);
}

.database-configuration .table-striped > tbody > tr:nth-of-type(odd) > td,
.database-configuration .table-striped > tbody > tr:nth-of-type(odd) > th {
  background-color: var(--bs-secondary-bg);
}

.database-configuration .table th {
  border-bottom-color: var(--bs-border-color);
  color: var(--bs-body-color);
  font-weight: 600;
}

.database-configuration .table td {
  border-top-color: var(--bs-border-color);
  color: var(--bs-body-color);
}

/* Badge Styling */
.database-configuration .badge {
  color: var(--bs-white);
}

.database-configuration .badge.bg-secondary {
  background-color: var(--bs-secondary) !important;
}

/* Code Styling */
.database-configuration code {
  color: var(--bs-code-color, #e83e8c);
  background-color: var(--bs-secondary-bg);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* Modal Styling */
.database-configuration .modal-content {
  background-color: var(--bs-body-bg);
  border-color: var(--bs-border-color);
  color: var(--bs-body-color);
}

.database-configuration .modal-header {
  border-bottom-color: var(--bs-border-color);
}

.database-configuration .modal-footer {
  border-top-color: var(--bs-border-color);
}

/* Form Styling */
.database-configuration .form-control {
  background-color: var(--bs-body-bg);
  border-color: var(--bs-border-color);
  color: var(--bs-body-color);
}

.database-configuration .form-control:focus {
  background-color: var(--bs-body-bg);
  border-color: var(--bs-primary);
  color: var(--bs-body-color);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}

.database-configuration .form-select {
  background-color: var(--bs-body-bg);
  border-color: var(--bs-border-color);
  color: var(--bs-body-color);
}

.database-configuration .form-select:focus {
  background-color: var(--bs-body-bg);
  border-color: var(--bs-primary);
  color: var(--bs-body-color);
  box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}

.database-configuration .form-label {
  color: var(--bs-body-color);
  font-weight: 500;
}

.database-configuration .form-text {
  color: var(--bs-secondary-color);
}

/* Button Styling */
.database-configuration .btn-outline-primary {
  color: var(--bs-primary);
  border-color: var(--bs-primary);
}

.database-configuration .btn-outline-primary:hover {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  color: var(--bs-white);
}

.database-configuration .btn-outline-success {
  color: var(--bs-success);
  border-color: var(--bs-success);
}

.database-configuration .btn-outline-success:hover {
  background-color: var(--bs-success);
  border-color: var(--bs-success);
  color: var(--bs-white);
}

.database-configuration .btn-outline-danger {
  color: var(--bs-danger);
  border-color: var(--bs-danger);
}

.database-configuration .btn-outline-danger:hover {
  background-color: var(--bs-danger);
  border-color: var(--bs-danger);
  color: var(--bs-white);
}

/* Alert Styling */
.database-configuration .alert {
  border-color: var(--bs-border-color);
}

.database-configuration .alert-info {
  background-color: rgba(var(--bs-info-rgb), 0.1);
  border-color: rgba(var(--bs-info-rgb), 0.2);
  color: var(--bs-info-text-emphasis);
}

.database-configuration .alert-warning {
  background-color: rgba(var(--bs-warning-rgb), 0.1);
  border-color: rgba(var(--bs-warning-rgb), 0.2);
  color: var(--bs-warning-text-emphasis);
}

.database-configuration .alert-success {
  background-color: rgba(var(--bs-success-rgb), 0.1);
  border-color: rgba(var(--bs-success-rgb), 0.2);
  color: var(--bs-success-text-emphasis);
}

.database-configuration .alert-danger {
  background-color: rgba(var(--bs-danger-rgb), 0.1);
  border-color: rgba(var(--bs-danger-rgb), 0.2);
  color: var(--bs-danger-text-emphasis);
}

/* Text Muted */
.database-configuration .text-muted {
  color: var(--bs-secondary-color) !important;
}

/* Small Text */
.database-configuration .small {
  color: var(--bs-body-color);
}

/* Spinner Styling */
.database-configuration .spinner-border {
  color: var(--bs-primary);
}

/* Tab Styling */
.database-configuration .nav-tabs {
  border-bottom-color: var(--bs-border-color);
}

.database-configuration .nav-tabs .nav-link {
  color: var(--bs-body-color);
  border-color: transparent;
}

.database-configuration .nav-tabs .nav-link:hover {
  border-color: var(--bs-border-color);
  color: var(--bs-primary);
}

.database-configuration .nav-tabs .nav-link.active {
  color: var(--bs-primary);
  background-color: var(--bs-body-bg);
  border-color: var(--bs-border-color) var(--bs-border-color) var(--bs-body-bg);
}

/* Dark Mode Specific Overrides */
[data-bs-theme="dark"] .database-configuration {
  --bs-code-color: #ff6b9d;
}

[data-bs-theme="dark"] .database-configuration .table {
  --bs-table-striped-bg: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .database-configuration .list-group-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .database-configuration .form-control:focus,
[data-bs-theme="dark"] .database-configuration .form-select:focus {
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
  .database-configuration .table-responsive {
    font-size: 0.875rem;
  }
  
  .database-configuration .btn-group .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .database-configuration {
    --bs-border-width: 2px;
  }
  
  .database-configuration .btn {
    border-width: 2px;
  }
  
  .database-configuration .form-control,
  .database-configuration .form-select {
    border-width: 2px;
  }
}
