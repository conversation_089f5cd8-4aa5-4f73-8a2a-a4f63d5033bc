[{"timestamp": "2025-09-04T12:45:50.282569", "table": "tenants", "action": "migrated", "count": 12, "error": null}, {"timestamp": "2025-09-04T12:45:50.285841", "table": "users", "action": "migrated", "count": 20, "error": null}, {"timestamp": "2025-09-04T12:45:50.288861", "table": "patients", "action": "migrated", "count": 89, "error": null}, {"timestamp": "2025-09-04T12:45:50.302642", "table": "departments", "action": "migrated", "count": 1004, "error": null}, {"timestamp": "2025-09-04T12:45:50.335233", "table": "test_master", "action": "migrated", "count": 299, "error": null}, {"timestamp": "2025-09-04T12:45:50.339604", "table": "samples", "action": "migrated", "count": 100, "error": null}, {"timestamp": "2025-09-04T12:45:50.354148", "table": "billings", "action": "migrated", "count": 111, "error": null}, {"timestamp": "2025-09-04T12:45:50.360752", "table": "results", "action": "migrated", "count": 212, "error": null}]