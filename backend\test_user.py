#!/usr/bin/env python3
"""
Test script to verify user data and login functionality
"""
from utils import read_data

def main():
    print("Testing user data...")
    
    try:
        users = read_data('users.json')
        soorya_user = next((u for u in users if u['username'] == 'soorya'), None)
        
        if soorya_user:
            print("✓ User found:")
            print(f"  ID: {soorya_user['id']}")
            print(f"  Username: {soorya_user['username']}")
            print(f"  Role: {soorya_user['role']}")
            print(f"  Tenant ID: {soorya_user['tenant_id']}")
            print(f"  Is Active: {soorya_user['is_active']} (type: {type(soorya_user['is_active'])})")
            
            # Test login logic
            username = 'soorya'
            password = '12345678'
            user = next((user for user in users if user['username'] == username and user['password'] == password), None)
            
            if user and user.get('is_active', True):
                print("✓ Login test passed - credentials and active status OK")
            else:
                print("✗ Login test failed")
                
            # Test role permissions
            if soorya_user['role'] in ['admin', 'hub_admin', 'developer']:
                print("✓ Role permissions test passed - developer role has admin access")
            else:
                print("✗ Role permissions test failed")
        else:
            print("✗ User not found")
            
    except Exception as e:
        print(f"✗ Error: {str(e)}")

if __name__ == "__main__":
    main()
