-- AVINI Labs Database Schema Design
-- SQLite Database Schema for migrating from JSON files
-- Generated based on analysis of 72 JSON files with 5,385 total records

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- Core Entity Tables

-- 1. Tenants (Franchises/Labs)
CREATE TABLE tenants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    site_code TEXT UNIQUE NOT NULL,
    address TEXT,
    city TEXT,
    state TEXT,
    pincode TEXT,
    contact_phone TEXT,
    email TEXT,
    contact_person TEXT,
    contact_person_phone TEXT,
    license_number TEXT,
    is_hub BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    use_site_code_prefix BOOLEAN DEFAULT TRUE,
    established_date DATE,
    franchise_fee DECIMAL(10,2),
    monthly_fee DECIMAL(10,2),
    commission_rate DECIMAL(5,2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. Users
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    email TEXT UNIQUE,
    first_name TEXT,
    last_name TEXT,
    role TEXT NOT NULL CHECK (role IN ('admin', 'hub_admin', 'franchise_admin', 'developer', 'lab_technician', 'receptionist')),
    tenant_id INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id)
);

-- 3. Patients
CREATE TABLE patients (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    patient_id TEXT UNIQUE NOT NULL,
    first_name TEXT NOT NULL,
    last_name TEXT,
    gender TEXT CHECK (gender IN ('Male', 'Female', 'Other')),
    date_of_birth DATE,
    phone TEXT,
    email TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    postal_code TEXT,
    blood_group TEXT,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Master Data Tables

-- 4. Departments
CREATE TABLE departments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. Test Categories
CREATE TABLE test_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    code TEXT UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. Sample Types
CREATE TABLE sample_types (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type_name TEXT NOT NULL,
    type_code TEXT UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 7. Containers
CREATE TABLE containers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    container_name TEXT NOT NULL,
    code TEXT UNIQUE,
    description TEXT,
    color_code TEXT,
    volume_required DECIMAL(8,2),
    additive TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. Test Master
CREATE TABLE test_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    test_name TEXT NOT NULL,
    test_code TEXT UNIQUE,
    hms_code TEXT,
    department_id INTEGER,
    category_id INTEGER,
    short_name TEXT,
    display_name TEXT,
    method TEXT,
    specimen TEXT,
    container_id INTEGER,
    reference_range TEXT,
    result_unit TEXT,
    decimals INTEGER DEFAULT 2,
    test_price DECIMAL(10,2),
    critical_low DECIMAL(10,2),
    critical_high DECIMAL(10,2),
    instructions TEXT,
    interpretation TEXT,
    notes TEXT,
    min_sample_qty DECIMAL(8,2),
    service_time INTEGER,
    reporting_days INTEGER,
    cutoff_time TIME,
    emergency_process_time INTEGER,
    emergency_process_period TEXT,
    expiry_time INTEGER,
    expiry_period TEXT,
    test_done_on TEXT,
    applicable_to TEXT,
    emr_classification TEXT,
    international_code TEXT,
    alert_message TEXT,
    alert_period TEXT,
    alert_sms BOOLEAN DEFAULT FALSE,
    special_report BOOLEAN DEFAULT FALSE,
    unacceptable_conditions TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (category_id) REFERENCES test_categories(id),
    FOREIGN KEY (container_id) REFERENCES containers(id)
);

-- 9. Samples
CREATE TABLE samples (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sample_id TEXT UNIQUE NOT NULL,
    patient_id INTEGER NOT NULL,
    sample_type_id INTEGER,
    container_id INTEGER,
    collection_date DATE,
    collection_time TIME,
    status TEXT DEFAULT 'collected' CHECK (status IN ('collected', 'processing', 'completed', 'rejected')),
    tenant_id INTEGER NOT NULL,
    collected_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id),
    FOREIGN KEY (sample_type_id) REFERENCES sample_types(id),
    FOREIGN KEY (container_id) REFERENCES containers(id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (collected_by) REFERENCES users(id)
);

-- 10. Billing
CREATE TABLE billings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    invoice_number TEXT UNIQUE NOT NULL,
    sid_number TEXT UNIQUE,
    patient_id INTEGER NOT NULL,
    subtotal DECIMAL(10,2) DEFAULT 0,
    discount DECIMAL(10,2) DEFAULT 0,
    tax DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    paid_amount DECIMAL(10,2) DEFAULT 0,
    balance DECIMAL(10,2) DEFAULT 0,
    payment_method TEXT,
    payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'partial', 'paid', 'refunded')),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'completed')),
    invoice_date DATE NOT NULL,
    due_date DATE,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 11. Billing Items
CREATE TABLE billing_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    billing_id INTEGER NOT NULL,
    test_id INTEGER,
    test_name TEXT NOT NULL,
    quantity INTEGER DEFAULT 1,
    price DECIMAL(10,2) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (billing_id) REFERENCES billings(id) ON DELETE CASCADE,
    FOREIGN KEY (test_id) REFERENCES test_master(id)
);

-- 12. Results
CREATE TABLE results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    result_id TEXT UNIQUE,
    sample_id INTEGER NOT NULL,
    test_id INTEGER NOT NULL,
    value TEXT,
    unit TEXT,
    reference_range TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'verified', 'rejected')),
    result_date DATE,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    verified_by INTEGER,
    verified_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (sample_id) REFERENCES samples(id),
    FOREIGN KEY (test_id) REFERENCES test_master(id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (verified_by) REFERENCES users(id)
);

-- Additional Master Data Tables

-- 13. Payment Methods
CREATE TABLE payment_methods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    method_name TEXT NOT NULL,
    method_code TEXT UNIQUE,
    description TEXT,
    is_online BOOLEAN DEFAULT FALSE,
    processing_fee DECIMAL(5,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 14. Roles
CREATE TABLE roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    description TEXT,
    permission_ids TEXT, -- JSON array of permission IDs
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 15. Permissions
CREATE TABLE permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    description TEXT,
    module TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for Performance
CREATE INDEX idx_patients_tenant_id ON patients(tenant_id);
CREATE INDEX idx_patients_patient_id ON patients(patient_id);
CREATE INDEX idx_samples_patient_id ON samples(patient_id);
CREATE INDEX idx_samples_tenant_id ON samples(tenant_id);
CREATE INDEX idx_billings_patient_id ON billings(patient_id);
CREATE INDEX idx_billings_tenant_id ON billings(tenant_id);
CREATE INDEX idx_billings_sid_number ON billings(sid_number);
CREATE INDEX idx_billing_items_billing_id ON billing_items(billing_id);
CREATE INDEX idx_results_sample_id ON results(sample_id);
CREATE INDEX idx_results_test_id ON results(test_id);
CREATE INDEX idx_results_tenant_id ON results(tenant_id);
CREATE INDEX idx_test_master_department_id ON test_master(department_id);
CREATE INDEX idx_test_master_category_id ON test_master(category_id);
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_users_username ON users(username);

-- Triggers for updated_at timestamps
CREATE TRIGGER update_tenants_timestamp 
    AFTER UPDATE ON tenants 
    BEGIN 
        UPDATE tenants SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id; 
    END;

CREATE TRIGGER update_users_timestamp 
    AFTER UPDATE ON users 
    BEGIN 
        UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id; 
    END;

CREATE TRIGGER update_patients_timestamp 
    AFTER UPDATE ON patients 
    BEGIN 
        UPDATE patients SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id; 
    END;

CREATE TRIGGER update_samples_timestamp 
    AFTER UPDATE ON samples 
    BEGIN 
        UPDATE samples SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id; 
    END;

CREATE TRIGGER update_billings_timestamp 
    AFTER UPDATE ON billings 
    BEGIN 
        UPDATE billings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id; 
    END;

CREATE TRIGGER update_results_timestamp
    AFTER UPDATE ON results
    BEGIN
        UPDATE results SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- Additional Master Data Tables (Extended Schema)

-- 16. Instruments
CREATE TABLE instruments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    model TEXT,
    manufacturer TEXT,
    serial_number TEXT UNIQUE,
    installation_date DATE,
    calibration_due DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 17. Reagents
CREATE TABLE reagents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    lot_number TEXT,
    expiry_date DATE,
    manufacturer TEXT,
    storage_temperature TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 18. Suppliers
CREATE TABLE suppliers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    contact_person TEXT,
    email TEXT,
    phone TEXT,
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 19. Inventory
CREATE TABLE inventory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    sku TEXT UNIQUE,
    category TEXT,
    description TEXT,
    quantity INTEGER DEFAULT 0,
    unit TEXT,
    reorder_level INTEGER DEFAULT 0,
    cost_price DECIMAL(10,2),
    selling_price DECIMAL(10,2),
    supplier_id INTEGER,
    location TEXT,
    expiry_date DATE,
    tenant_id INTEGER NOT NULL,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (tenant_id) REFERENCES tenants(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 20. Test Methods
CREATE TABLE test_methods (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    principle TEXT,
    procedure TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 21. Test Parameters
CREATE TABLE test_parameters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    parameter_name TEXT NOT NULL,
    parameter_code TEXT UNIQUE,
    unit TEXT,
    reference_range TEXT,
    method TEXT,
    category_id INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES test_categories(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 22. Units of Measurement
CREATE TABLE units_of_measurement (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    description TEXT NOT NULL,
    symbol TEXT,
    type TEXT,
    conversion_factor DECIMAL(10,6) DEFAULT 1.0,
    technical BOOLEAN DEFAULT FALSE,
    inventory BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 23. Specimen Master
CREATE TABLE specimen_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    specimen TEXT NOT NULL,
    container TEXT,
    disposable BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 24. Container Master
CREATE TABLE container_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    description TEXT NOT NULL,
    short_name TEXT,
    color TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 25. Method Master
CREATE TABLE method_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    method TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 26. Organism Master
CREATE TABLE organism_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,
    description TEXT NOT NULL,
    no_growth BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 27. Antibiotic Master
CREATE TABLE antibiotic_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    antibiotic_code TEXT UNIQUE NOT NULL,
    antibiotic_group TEXT,
    antibiotic_description TEXT NOT NULL,
    antibiotic_content TEXT,
    order_sequence INTEGER,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 28. Organism vs Antibiotic
CREATE TABLE organism_vs_antibiotic (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    organism_id INTEGER NOT NULL,
    antibiotic_id INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (organism_id) REFERENCES organism_master(id),
    FOREIGN KEY (antibiotic_id) REFERENCES antibiotic_master(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    UNIQUE(organism_id, antibiotic_id)
);

-- 29. Main Department Master
CREATE TABLE main_department_master (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    major_department TEXT NOT NULL,
    code TEXT UNIQUE NOT NULL,
    department TEXT NOT NULL,
    order_sequence INTEGER,
    short_name TEXT,
    queue TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 30. Department Settings
CREATE TABLE department_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    main TEXT,
    code TEXT UNIQUE NOT NULL,
    sub_name TEXT,
    service_time INTEGER,
    room TEXT,
    order_sequence INTEGER,
    dept_amt DECIMAL(10,2),
    short TEXT,
    collect BOOLEAN DEFAULT FALSE,
    process_receive BOOLEAN DEFAULT FALSE,
    receive BOOLEAN DEFAULT FALSE,
    no INTEGER,
    pending BOOLEAN DEFAULT FALSE,
    dept TEXT,
    barcode BOOLEAN DEFAULT FALSE,
    appt BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Additional indexes for new tables
CREATE INDEX idx_instruments_serial_number ON instruments(serial_number);
CREATE INDEX idx_reagents_lot_number ON reagents(lot_number);
CREATE INDEX idx_inventory_sku ON inventory(sku);
CREATE INDEX idx_inventory_tenant_id ON inventory(tenant_id);
CREATE INDEX idx_inventory_supplier_id ON inventory(supplier_id);
CREATE INDEX idx_test_parameters_category_id ON test_parameters(category_id);
CREATE INDEX idx_organism_vs_antibiotic_organism_id ON organism_vs_antibiotic(organism_id);
CREATE INDEX idx_organism_vs_antibiotic_antibiotic_id ON organism_vs_antibiotic(antibiotic_id);

-- Additional triggers for new tables
CREATE TRIGGER update_instruments_timestamp
    AFTER UPDATE ON instruments
    BEGIN
        UPDATE instruments SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER update_reagents_timestamp
    AFTER UPDATE ON reagents
    BEGIN
        UPDATE reagents SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER update_suppliers_timestamp
    AFTER UPDATE ON suppliers
    BEGIN
        UPDATE suppliers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER update_inventory_timestamp
    AFTER UPDATE ON inventory
    BEGIN
        UPDATE inventory SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
