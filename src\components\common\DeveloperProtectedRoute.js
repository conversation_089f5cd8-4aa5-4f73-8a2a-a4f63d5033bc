import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { Alert, Container } from 'react-bootstrap';

/**
 * Protected route component that only allows access to users with 'developer' role
 * Used specifically for database-related admin settings
 */
const DeveloperProtectedRoute = ({ children }) => {
  const { isAuthenticated, loading: authLoading, currentUser } = useAuth();

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '50vh' }}>
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2 text-muted">Checking permissions...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Check if user has developer role
  const isDeveloper = currentUser?.role === 'developer';

  // If user doesn't have developer role, show access denied message
  if (!isDeveloper) {
    return (
      <Container className="mt-5">
        <Alert variant="danger" className="text-center">
          <Alert.Heading>Access Denied</Alert.Heading>
          <p>
            You don't have permission to access this page. This section is restricted to users with Developer role.
          </p>
          <hr />
          <p className="mb-0">
            Contact your system administrator if you believe you should have access to this feature.
          </p>
        </Alert>
      </Container>
    );
  }

  // User has developer role, render the protected content
  return children;
};

export default DeveloperProtectedRoute;
