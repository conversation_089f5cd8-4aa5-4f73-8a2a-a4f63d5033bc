#!/usr/bin/env python3
"""
Check if MYD098 report has notes field
"""

import json
import sys
import os

def check_myd098_notes():
    """Check if MYD098 report has notes field"""
    
    # Read the billing reports file
    reports_file = 'backend/data/billing_reports.json'
    
    if not os.path.exists(reports_file):
        print(f"❌ File not found: {reports_file}")
        return
    
    try:
        with open(reports_file, 'r') as f:
            reports = json.load(f)
        
        print(f"📊 Total reports in file: {len(reports)}")
        
        # Find the MYD098 report
        myd098_report = None
        for report in reports:
            if report.get('sid_number') == 'MYD098':
                myd098_report = report
                break
        
        if myd098_report:
            print(f"\n✅ Found MYD098 report:")
            print(f"  Report ID: {myd098_report.get('id')}")
            print(f"  SID: {myd098_report.get('sid_number')}")
            print(f"  Billing ID: {myd098_report.get('billing_id')}")
            print(f"  Has notes field: {'notes' in myd098_report}")
            
            if 'notes' in myd098_report:
                notes_value = myd098_report.get('notes', '')
                print(f"  Notes value: \"{notes_value}\"")
                print(f"  Notes length: {len(notes_value)}")
            else:
                print(f"  ❌ Notes field is missing from the report!")
                print(f"  Report top-level keys: {list(myd098_report.keys())}")
                
                # Check if it's in financial_summary or elsewhere
                if 'financial_summary' in myd098_report:
                    fs = myd098_report['financial_summary']
                    print(f"  Financial summary keys: {list(fs.keys())}")
                    
            # Also check the corresponding billing record
            billing_id = myd098_report.get('billing_id')
            if billing_id:
                print(f"\n🔍 Checking corresponding billing record (ID: {billing_id}):")
                
                billings_file = 'backend/data/billings.json'
                if os.path.exists(billings_file):
                    with open(billings_file, 'r') as f:
                        billings = json.load(f)
                    
                    billing_record = None
                    for billing in billings:
                        if billing.get('id') == billing_id:
                            billing_record = billing
                            break
                    
                    if billing_record:
                        print(f"  ✅ Found billing record")
                        print(f"  Has notes field: {'notes' in billing_record}")
                        if 'notes' in billing_record:
                            billing_notes = billing_record.get('notes', '')
                            print(f"  Billing notes value: \"{billing_notes}\"")
                        else:
                            print(f"  ❌ Notes field missing from billing record too!")
                    else:
                        print(f"  ❌ Billing record not found")
                else:
                    print(f"  ❌ Billings file not found")
        else:
            print('❌ MYD098 report not found')
            
            # Show available SIDs for reference
            sids = [r.get('sid_number') for r in reports[:10]]
            print(f"Available SIDs (first 10): {sids}")
            
    except Exception as e:
        print(f"❌ Error reading file: {str(e)}")

def check_all_reports_notes():
    """Check how many reports have notes field"""

    reports_file = 'backend/data/billing_reports.json'

    if not os.path.exists(reports_file):
        print(f"❌ File not found: {reports_file}")
        return

    try:
        with open(reports_file, 'r') as f:
            reports = json.load(f)

        # Check how many reports have notes field
        reports_with_notes = 0
        reports_with_non_empty_notes = 0

        for report in reports:
            if 'notes' in report:
                reports_with_notes += 1
                if report.get('notes', '').strip():
                    reports_with_non_empty_notes += 1

        print(f'\n📊 NOTES FIELD ANALYSIS:')
        print(f'  Total reports: {len(reports)}')
        print(f'  Reports with notes field: {reports_with_notes}')
        print(f'  Reports with non-empty notes: {reports_with_non_empty_notes}')
        print(f'  Reports missing notes field: {len(reports) - reports_with_notes}')

        # Show a few examples
        print(f'\n🔍 First 5 reports notes status:')
        for i, report in enumerate(reports[:5]):
            sid = report.get('sid_number', 'Unknown')
            has_notes = 'notes' in report
            notes_value = report.get('notes', 'MISSING') if has_notes else 'MISSING'
            print(f'  {i+1}. SID {sid}: has_notes={has_notes}, value="{notes_value}"')

    except Exception as e:
        print(f"❌ Error reading file: {str(e)}")

if __name__ == "__main__":
    check_myd098_notes()
    check_all_reports_notes()
