#!/usr/bin/env python3
"""
Simple test to check admin route access
"""
import requests
import json

def test_simple_admin():
    """Test a simple admin endpoint"""
    print("Testing simple admin access...")
    
    # Login first
    login_data = {
        "username": "admin",  # Try with known admin user first
        "password": "admin123"
    }
    
    try:
        # Login with admin user
        response = requests.post(
            "http://localhost:5002/api/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            user = data.get('user')
            
            print(f"✓ Admin login successful - Role: {user.get('role')}")
            
            # Test users endpoint with admin
            headers = {"Authorization": f"Bearer {token}"}
            users_response = requests.get(
                "http://localhost:5002/api/admin/users",
                headers=headers
            )
            
            print(f"Admin users API status: {users_response.status_code}")
            
            if users_response.status_code == 200:
                users_data = users_response.json()
                print(f"✓ Admin can access users API - Found {len(users_data)} users")
            else:
                print(f"✗ Admin cannot access users API: {users_response.text}")
        else:
            print(f"✗ Admin login failed: {response.text}")
        
        # Now test with developer user
        print("\n" + "="*50)
        print("Testing with developer user...")
        
        dev_login_data = {
            "username": "soorya",
            "password": "12345678"
        }
        
        dev_response = requests.post(
            "http://localhost:5002/api/auth/login",
            json=dev_login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if dev_response.status_code == 200:
            dev_data = dev_response.json()
            dev_token = dev_data.get('token')
            dev_user = dev_data.get('user')
            
            print(f"✓ Developer login successful - Role: {dev_user.get('role')}")
            
            # Test users endpoint with developer
            dev_headers = {"Authorization": f"Bearer {dev_token}"}
            dev_users_response = requests.get(
                "http://localhost:5002/api/admin/users",
                headers=dev_headers
            )
            
            print(f"Developer users API status: {dev_users_response.status_code}")
            
            if dev_users_response.status_code == 200:
                dev_users_data = dev_users_response.json()
                print(f"✓ Developer can access users API - Found {len(dev_users_data)} users")
            else:
                print(f"✗ Developer cannot access users API: {dev_users_response.text}")
        else:
            print(f"✗ Developer login failed: {dev_response.text}")
            
    except Exception as e:
        print(f"✗ Error: {str(e)}")

if __name__ == "__main__":
    test_simple_admin()
