#!/usr/bin/env python3
"""
Database Analysis Script for AVINI Labs JSON to SQLite Migration
Analyzes all JSON files to understand data structures and relationships
"""

import json
import os
from collections import defaultdict
from datetime import datetime

def analyze_json_structure(file_path):
    """Analyze the structure of a JSON file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if isinstance(data, list) and len(data) > 0:
            # Analyze first item to understand structure
            sample_item = data[0]
            return {
                'type': 'array',
                'count': len(data),
                'sample_structure': analyze_object_structure(sample_item),
                'file_size_mb': os.path.getsize(file_path) / (1024 * 1024)
            }
        elif isinstance(data, dict):
            return {
                'type': 'object',
                'structure': analyze_object_structure(data),
                'file_size_mb': os.path.getsize(file_path) / (1024 * 1024)
            }
        else:
            return {
                'type': type(data).__name__,
                'value': data,
                'file_size_mb': os.path.getsize(file_path) / (1024 * 1024)
            }
    except Exception as e:
        return {'error': str(e)}

def analyze_object_structure(obj):
    """Analyze the structure of a JSON object"""
    if not isinstance(obj, dict):
        return type(obj).__name__
    
    structure = {}
    for key, value in obj.items():
        if isinstance(value, dict):
            structure[key] = 'object'
        elif isinstance(value, list):
            if len(value) > 0:
                structure[key] = f'array[{type(value[0]).__name__}]'
            else:
                structure[key] = 'array[empty]'
        else:
            structure[key] = type(value).__name__
    
    return structure

def find_relationships(data_files):
    """Find potential relationships between data files"""
    relationships = defaultdict(list)
    
    # Common foreign key patterns
    fk_patterns = ['_id', 'Id', 'ID']
    
    for file_name, analysis in data_files.items():
        if analysis.get('type') == 'array' and 'sample_structure' in analysis:
            structure = analysis['sample_structure']
            for field, field_type in structure.items():
                # Look for foreign key patterns
                for pattern in fk_patterns:
                    if pattern in field and field_type in ['int', 'str']:
                        relationships[file_name].append({
                            'field': field,
                            'type': field_type,
                            'potential_reference': field.replace(pattern, '')
                        })
    
    return relationships

def main():
    """Main analysis function"""
    data_dir = 'backend/data'
    
    # Get all JSON files (excluding backups)
    json_files = []
    for file in os.listdir(data_dir):
        if file.endswith('.json') and 'backup' not in file.lower():
            json_files.append(file)
    
    print(f"Found {len(json_files)} JSON files to analyze")
    print("=" * 80)
    
    # Analyze each file
    data_files = {}
    for file_name in sorted(json_files):
        file_path = os.path.join(data_dir, file_name)
        print(f"\nAnalyzing: {file_name}")
        
        analysis = analyze_json_structure(file_path)
        data_files[file_name] = analysis
        
        if 'error' in analysis:
            print(f"  ERROR: {analysis['error']}")
        else:
            print(f"  Type: {analysis['type']}")
            if analysis['type'] == 'array':
                print(f"  Records: {analysis['count']}")
                print(f"  Size: {analysis['file_size_mb']:.2f} MB")
                if 'sample_structure' in analysis:
                    print(f"  Fields: {list(analysis['sample_structure'].keys())}")
            elif analysis['type'] == 'object':
                print(f"  Size: {analysis['file_size_mb']:.2f} MB")
                print(f"  Keys: {list(analysis['structure'].keys())}")
    
    # Find relationships
    print("\n" + "=" * 80)
    print("RELATIONSHIP ANALYSIS")
    print("=" * 80)
    
    relationships = find_relationships(data_files)
    for file_name, rels in relationships.items():
        if rels:
            print(f"\n{file_name}:")
            for rel in rels:
                print(f"  - {rel['field']} ({rel['type']}) -> {rel['potential_reference']}")
    
    # Generate summary
    print("\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    
    total_records = 0
    total_size = 0
    
    for file_name, analysis in data_files.items():
        if analysis.get('type') == 'array':
            total_records += analysis.get('count', 0)
        total_size += analysis.get('file_size_mb', 0)
    
    print(f"Total JSON files: {len(json_files)}")
    print(f"Total records: {total_records:,}")
    print(f"Total size: {total_size:.2f} MB")
    
    # Save detailed analysis
    with open('json_analysis_report.json', 'w') as f:
        json.dump({
            'timestamp': datetime.now().isoformat(),
            'files_analyzed': len(json_files),
            'total_records': total_records,
            'total_size_mb': total_size,
            'file_analysis': data_files,
            'relationships': dict(relationships)
        }, f, indent=2)
    
    print(f"\nDetailed analysis saved to: json_analysis_report.json")

if __name__ == "__main__":
    main()
